/**
 * Trang hiển thị danh sách các gói R-Point
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography, ResponsiveGrid, Loading } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import RPointPackageCard from '../components/RPointPackageCard';
import CustomRPointPackageCard from '../components/CustomRPointPackageCard';
import { useRPointPackagesData } from '../hooks/useRPointPackagesData';
import { RPointPackage } from '../types/rpoint.types';

/**
 * Trang hiển thị danh sách các gói R-Point
 */
const RPointPackagesPage: React.FC = () => {
  const { t } = useTranslation(['rpoint', 'common']);
  const navigate = useNavigate();
  const { packages, customPackage, isLoading, error } = useRPointPackagesData();

  // Sử dụng hook theme
  useTheme();

  // Xử lý khi chọn gói
  const handleSelectPackage = (pkg: RPointPackage) => {
    console.log('Selected package:', pkg);
    // Chuyển hướng đến trang đơn hàng với thông tin gói
    navigate('/rpoint/order', {
      state: {
        packageInfo: {
          id: pkg.id, // pkg.id đã là number rồi
          points: pkg.points,
          price: pkg.price,
          name: pkg.description ? pkg.description : `Gói ${pkg.points} R-Point`,
        },
      },
    });
  };

  // Xử lý khi chọn gói tùy chỉnh
  const handleSelectCustomPackage = (amount: number, points: number) => {
    console.log('Selected custom package:', { amount, points });
    // Chuyển hướng đến trang đơn hàng với thông tin gói tùy chỉnh
    navigate('/rpoint/order', {
      state: {
        packageInfo: {
          id: 0, // Gán ID mặc định cho gói tùy chỉnh
          points: points,
          price: amount,
          name: `Gói tùy chỉnh ${points} R-Point`,
        },
      },
    });
  };

  return (
    <div>
      {/* Breadcrumb */}

      <div className="py-8">
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <Loading />
          </div>
        )}

        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-lg mb-6">
            <Typography variant="body1">
              {t('common:error.loading', 'Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.')}
            </Typography>
          </div>
        )}

        {!isLoading && !error && (
          <>
            {/* Gói thường */}
            <div className="mb-12">
              <Typography variant="h5" className="mb-6">
                {t('rpoint:packages.standardPackages', 'Gói thường')}
              </Typography>

              <ResponsiveGrid
                maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
                maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
                gap={2}
              >
                {packages.map((pkg: RPointPackage) => (
                  <RPointPackageCard key={pkg.id} package={pkg} onSelect={handleSelectPackage} />
                ))}
              </ResponsiveGrid>
            </div>

            {/* Gói tùy chỉnh */}
            <div>
              <Typography variant="h5" className="mb-6">
                {t('rpoint:packages.customPackage', 'Gói tùy chỉnh')}
              </Typography>

              <div className="max-w-md mx-auto">
                <CustomRPointPackageCard
                  customPackage={customPackage}
                  onSelect={handleSelectCustomPackage}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RPointPackagesPage;
