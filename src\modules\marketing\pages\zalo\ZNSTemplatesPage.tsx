import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, Chip, ActionMenu, ConfirmDeleteModal } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import {
  useZNSTemplates,
  useBulkDeleteZNSTemplates,
  useSyncZNSTemplates,
} from '@/modules/marketing/hooks/useZNSTemplatesQuery';
import AddZaloZNSTemplateSlideForm from '@/modules/marketing/components/zns/forms/AddZaloZNSTemplateSlideForm';
import EditZNSTemplateForm from '@/modules/marketing/components/zns/EditZNSTemplateForm';
import type { ActionMenuItem } from '@/shared/components/common/ActionMenu/ActionMenu';
import type { ZNSTemplateDto } from '@/modules/marketing/types/zalo.types';
import { ZNSTemplateStatus } from '@/modules/marketing/types/zalo.types';
import type { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { formatTimestamp } from '@/shared/utils/date';

const ZNSTemplatesPage: React.FC = () => {
  const { t } = useTranslation(['common', 'marketing']);

  // Slide form hooks
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ZNSTemplateStatus | ''>('');
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string>('');

  // Edit template state
  const [editingTemplate, setEditingTemplate] = useState<ZNSTemplateDto | null>(null);
  const [integrationId, setIntegrationId] = useState<string>(''); // You might need to get this from context or props

  // Bulk delete states
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Cấu hình columns cho table
  const columns = useMemo(
    () => [
      {
        key: 'templateName',
        title: t('marketing:zalo.znsTemplates.table.templateName', 'Tên Template'),
        dataIndex: 'templateName',
        sortable: true,
        width: '200px',
      },
      {
        key: 'status',
        title: t('marketing:zalo.znsTemplates.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '120px',
        render: (value: unknown) => {
          const status = value as string;
          const statusConfig: Record<
            string,
            { variant: 'warning' | 'success' | 'danger' | 'secondary' | 'default'; label: string }
          > = {
            [ZNSTemplateStatus.PENDING_REVIEW]: {
              variant: 'warning',
              label: t('marketing:zalo.znsTemplates.status.pending', 'Chờ duyệt'),
            },
            [ZNSTemplateStatus.ENABLE]: {
              variant: 'success',
              label: t('marketing:zalo.znsTemplates.status.approved', 'Đã duyệt'),
            },
            [ZNSTemplateStatus.REJECT]: {
              variant: 'danger',
              label: t('marketing:zalo.znsTemplates.status.rejected', 'Bị từ chối'),
            },
            [ZNSTemplateStatus.DISABLE]: {
              variant: 'secondary',
              label: t('marketing:zalo.znsTemplates.status.disabled', 'Vô hiệu hóa'),
            },
          };
          const config = statusConfig[status] || { variant: 'default' as const, label: status };
          return (
            <Chip variant={config.variant} size="sm">
              {config.label}
            </Chip>
          );
        },
      },
      {
        key: 'templateTag',
        title: t('marketing:zalo.znsTemplates.table.templateTag', 'Loại Template'),
        dataIndex: 'templateTag',
        width: '150px',
        render: (value: unknown) => {
          const tag = value as string;
          const tagConfig: Record<
            string,
            { variant: 'primary' | 'info' | 'secondary' | 'default'; label: string }
          > = {
            TRANSACTION: {
              variant: 'primary',
              label: t('marketing:zalo.znsTemplates.tag.transaction', 'Giao dịch'),
            },
            PROMOTION: {
              variant: 'info',
              label: t('marketing:zalo.znsTemplates.tag.promotion', 'Khuyến mãi'),
            },
            OTP: { variant: 'secondary', label: t('marketing:zalo.znsTemplates.tag.otp', 'OTP') },
          };
          const config = tagConfig[tag] || { variant: 'default' as const, label: tag };

          if (!tag) return null;
          return (
            <Chip variant={config.variant} size="sm">
              {config.label}
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('marketing:zalo.znsTemplates.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        sortable: true,
        width: '150px',
        render: (value: unknown) => {
          return formatTimestamp(value);
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '80px',
        render: (_: unknown, record: ZNSTemplateDto) => {
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleViewTemplate(record),
            },
          ];

          // Debug log để kiểm tra status
          console.log('Template status:', record.status, 'Expected:', ZNSTemplateStatus.REJECT);

          // Chỉ hiển thị nút chỉnh sửa cho template có status REJECT
          // Kiểm tra cả REJECT và rejected (case insensitive)
          if (
            record.status === ZNSTemplateStatus.REJECT ||
            record.status?.toLowerCase() === 'reject' ||
            record.status?.toLowerCase() === 'rejected'
          ) {
            actionItems.push({
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditTemplate(record),
            });
          }

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="200px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t]
  );

  // Cấu hình data table
  const dataTable = useDataTable(
    useDataTableConfig({
      columns,
      createQueryParams: params => ({
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
        status: statusFilter || undefined,
        templateTag: templateTypeFilter || undefined,
      }),
    })
  );

  // Query parameters
  const queryParams = useMemo(
    () => ({
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: searchTerm,
      sortBy: dataTable.tableData.sortBy || undefined,
      sortDirection: dataTable.tableData.sortDirection || undefined,
      status: statusFilter || undefined,
      templateTag: templateTypeFilter || undefined,
    }),
    [
      dataTable.tableData.currentPage,
      dataTable.tableData.pageSize,
      searchTerm,
      dataTable.tableData.sortBy,
      dataTable.tableData.sortDirection,
      statusFilter,
      templateTypeFilter,
    ]
  );

  // Fetch data
  const { data: apiResponse, isLoading, refetch } = useZNSTemplates(queryParams);

  // Bulk delete hook
  const bulkDeleteMutation = useBulkDeleteZNSTemplates();

  // Sync templates hook
  const syncTemplatesMutation = useSyncZNSTemplates();

  const data = apiResponse?.result;

  // Column visibility
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility[]>(
    columns.map(col => ({
      id: col.key,
      label: col.title,
      visible: true,
    }))
  );

  // Handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleAddTemplate = () => {
    showCreateForm();
  };

  const handleViewTemplate = (template: ZNSTemplateDto) => {
    console.log('View template:', template);
    // TODO: Implement view template logic
  };

  const handleEditTemplate = (template: ZNSTemplateDto) => {
    // Chỉ cho phép chỉnh sửa template có status REJECT
    const isRejected =
      template.status === ZNSTemplateStatus.REJECT ||
      template.status?.toLowerCase() === 'reject' ||
      template.status?.toLowerCase() === 'rejected';

    if (!isRejected) {
      console.log('Template không thể chỉnh sửa, status:', template.status);
      return;
    }

    console.log('Opening edit form for template:', template.id, 'status:', template.status);
    setEditingTemplate(template);
    setIntegrationId('default-integration-id'); // You should get this from context or props
    showEditForm();
  };

  const handleEditSuccess = () => {
    setEditingTemplate(null);
    hideEditForm();
    // Refresh data
    refetch();
  };

  const handleSyncTemplates = async () => {
    try {
      await syncTemplatesMutation.mutateAsync(); // API không cần integrationId nữa
      refetch(); // Reload templates list after sync
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  const handleColumnVisibilityChange = (columns: ColumnVisibility[]) => {
    setColumnVisibility(columns);
  };

  const handleCreateSuccess = () => {
    hideCreateForm();
    refetch();
  };

  const handleCreateCancel = () => {
    hideCreateForm();
  };

  // Bulk delete handlers
  const handleSelectionChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) return;
    setShowDeleteModal(true);
  };

  const handleConfirmBulkDelete = async () => {
    if (selectedRowKeys.length === 0) return;

    setIsDeleting(true);
    try {
      // Convert selectedRowKeys to numbers (template IDs)
      const templateIds = selectedRowKeys.map(key => Number(key));
      await bulkDeleteMutation.mutateAsync(templateIds);

      // Reset selection and close modal
      setSelectedRowKeys([]);
      setShowDeleteModal(false);

      // Refresh data
      refetch();
    } catch (error) {
      console.error('Bulk delete failed:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelBulkDelete = () => {
    setShowDeleteModal(false);
  };

  // Filter menu items
  const filterItems = [
    {
      id: 'status-filter',
      label: t('marketing:zalo.znsTemplates.filter.status', 'Lọc theo trạng thái'),
      children: [
        {
          id: 'status-all',
          label: t('common:all', 'Tất cả'),
          onClick: () => setStatusFilter(''),
        },
        {
          id: 'status-pending',
          label: t('marketing:zalo.znsTemplates.status.pending', 'Chờ duyệt'),
          onClick: () => setStatusFilter(ZNSTemplateStatus.PENDING_REVIEW),
        },
        {
          id: 'status-approved',
          label: t('marketing:zalo.znsTemplates.status.approved', 'Đã duyệt'),
          onClick: () => setStatusFilter(ZNSTemplateStatus.ENABLE),
        },
        {
          id: 'status-rejected',
          label: t('marketing:zalo.znsTemplates.status.rejected', 'Bị từ chối'),
          onClick: () => setStatusFilter(ZNSTemplateStatus.REJECT),
        },
      ],
    },
    {
      id: 'type-filter',
      label: t('marketing:zalo.znsTemplates.filter.type', 'Lọc theo loại'),
      children: [
        {
          id: 'type-all',
          label: t('common:all', 'Tất cả'),
          onClick: () => setTemplateTypeFilter(''),
        },
        {
          id: 'type-transaction',
          label: t('marketing:zalo.znsTemplates.tag.transaction', 'Giao dịch'),
          onClick: () => setTemplateTypeFilter('TRANSACTION'),
        },
        {
          id: 'type-promotion',
          label: t('marketing:zalo.znsTemplates.tag.promotion', 'Khuyến mãi'),
          onClick: () => setTemplateTypeFilter('PROMOTION'),
        },
        {
          id: 'type-otp',
          label: t('marketing:zalo.znsTemplates.tag.otp', 'OTP'),
          onClick: () => setTemplateTypeFilter('OTP'),
        },
      ],
    },
  ];

  // Additional icons for MenuIconBar
  const additionalIcons = [
    {
      id: 'sync-templates',
      icon: 'folder-sync',
      tooltip: t('marketing:zalo.znsTemplates.sync', 'Đồng bộ templates từ Zalo'),
      onClick: handleSyncTemplates,
      variant: 'default' as const,
      disabled: syncTemplatesMutation.isPending,
    },
    {
      id: 'refresh-data',
      icon: 'refresh-cw',
      tooltip: t('common:refresh', 'Làm mới dữ liệu'),
      onClick: () => refetch(),
      variant: 'default' as const,
    },
    // Bulk delete icon - only show when items are selected
    ...(selectedRowKeys.length > 0
      ? [
          {
            id: 'bulk-delete',
            icon: 'trash',
            tooltip: t('common:delete', 'Xóa đã chọn'),
            onClick: handleBulkDelete,
            variant: 'secondary' as const,
          },
        ]
      : []),
  ];

  // Visible columns based on column visibility
  const visibleColumns = columns.filter(
    col => columnVisibility.find(cv => cv.id === col.key)?.visible !== false
  );

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddTemplate}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        columns={columnVisibility}
        items={filterItems}
        additionalIcons={additionalIcons}
      />
      <SlideInForm isVisible={isCreateVisible}>
        <AddZaloZNSTemplateSlideForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>

      {/* Edit Template Form */}
      <SlideInForm isVisible={isEditVisible}>
        {editingTemplate && (
          <EditZNSTemplateForm
            template={editingTemplate}
            integrationId={integrationId}
            onClose={hideEditForm}
            onSuccess={handleEditSuccess}
          />
        )}
      </SlideInForm>

      {/* Content */}
      <Card>
        <Table
          columns={visibleColumns}
          data={data?.items || []}
          loading={isLoading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          pagination={{
            current: data?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: data?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
          }}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
        />
      </Card>

      {/* Create Form Slide */}

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        itemCount={selectedRowKeys.length}
        isSubmitting={isDeleting}
      />
    </div>
  );
};

export default ZNSTemplatesPage;
