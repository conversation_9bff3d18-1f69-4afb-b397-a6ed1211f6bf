import React, { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading, Button, Icon } from '@/shared/components/common';

// Import R-Point pages
const RPointPackagesPage = lazy(() => import('@/modules/rpoint/pages/RPointPackagesPage'));
const RPointOrderPage = lazy(() => import('@/modules/rpoint/pages/RPointOrderPage'));
const RPointPaymentPage = lazy(() => import('@/modules/rpoint/pages/RPointPaymentPage'));

// Wrapper component cho RPointPackagesPage với nút plus
const RPointPackagesPageWrapper: React.FC = () => {
  const navigate = useNavigate();

  // Tạo nút plus để chuyển đến trang R-Point packages
  const plusButton = (
    <Button
      variant="primary"
      size="sm"
      onClick={() => navigate('/rpoint/packages')}
      leftIcon={<Icon name="plus" size="sm" />}
    >
      R-Point
    </Button>
  );

  return (
    <MainLayout title="rpoint:breadcrumb.packages" actions={plusButton}>
      <Suspense fallback={<Loading />}>
        <RPointPackagesPage />
      </Suspense>
    </MainLayout>
  );
};

/**
 * R-Point module routes
 */
const rpointRoutes: RouteObject[] = [
  {
    path: '/rpoint/packages',
    element: <RPointPackagesPageWrapper />,
  },
  {
    path: '/rpoint/order',
    element: (
      <MainLayout title="rpoint:breadcrumb.order">
        <Suspense fallback={<Loading />}>
          <RPointOrderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/rpoint/payment/:id',
    element: (
      <MainLayout title="rpoint:breadcrumb.payment">
        <Suspense fallback={<Loading />}>
          <RPointPaymentPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default rpointRoutes;
