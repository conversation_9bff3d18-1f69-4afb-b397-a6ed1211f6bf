import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { ZNSTemplateDto, ZNSTemplateQueryDto } from '../../types/zalo.types';

/**
 * API Layer - ZNS Templates
 * Gọi API thô không xử lý logic nghiệp vụ
 */

/**
 * L<PERSON>y tất cả ZNS templates từ database
 */
export const getAllZNSTemplates = async (
  params?: ZNSTemplateQueryDto
): Promise<ApiResponseDto<PaginatedResult<ZNSTemplateDto>>> => {
  const response = await apiClient.get<PaginatedResult<ZNSTemplateDto>>(
    '/marketing/zalo/zns/templates/all',
    { params }
  );
  return response;
};

/**
 * Lấy chi tiết ZNS template theo ID
 */
export const getZNSTemplateById = async (
  templateId: string
): Promise<ApiResponseDto<ZNSTemplateDto>> => {
  const response = await apiClient.get<ZNSTemplateDto>(
    `/marketing/zalo/zns/templates/${templateId}`
  );
  return response;
};

/**
 * Tạo ZNS template mới
 */
export const createZNSTemplate = async (
  data: Omit<ZNSTemplateDto, 'id' | 'createdAt' | 'updatedAt'>
): Promise<ApiResponseDto<ZNSTemplateDto>> => {
  const response = await apiClient.post<ZNSTemplateDto>('/marketing/zalo/zns/templates', data);
  return response;
};

/**
 * Cập nhật ZNS template
 */
export const updateZNSTemplate = async (
  templateId: string,
  data: Partial<Omit<ZNSTemplateDto, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<ApiResponseDto<ZNSTemplateDto>> => {
  const response = await apiClient.put<ZNSTemplateDto>(
    `/marketing/zalo/zns/templates/${templateId}`,
    data
  );
  return response;
};

/**
 * Xóa ZNS template
 */
export const deleteZNSTemplate = async (templateId: string): Promise<ApiResponseDto<void>> => {
  const response = await apiClient.delete<void>(`/marketing/zalo/zns/templates/${templateId}`);
  return response;
};

/**
 * Xóa nhiều ZNS templates cùng lúc
 */
export const bulkDeleteZNSTemplates = async (
  templateIds: number[]
): Promise<ApiResponseDto<void>> => {
  const response = await apiClient.delete<void>('/marketing/zalo/zns/templates/bulk-delete', {
    data: { templateIds },
  });
  return response;
};

/**
 * Chỉnh sửa template ZNS đã bị từ chối (REJECT) theo chuẩn Zalo API
 * API endpoint: PUT /v1/marketing/zalo/zns/{integrationId}/templates/{templateId}/edit
 */
export const editZNSTemplate = async (
  integrationId: string,
  templateId: string,
  data: {
    template_name: string;
    template_type: number;
    tag: string;
    layout: {
      body: {
        components: Array<{
          TITLE?: { value: string };
          PARAGRAPH?: { value: string };
        }>;
      };
      header?: {
        components: Array<{
          TITLE?: { value: string };
          PARAGRAPH?: { value: string };
        }>;
      };
      footer?: {
        components: Array<{
          TITLE?: { value: string };
          PARAGRAPH?: { value: string };
        }>;
      };
    };
    params?: Array<{
      name: string;
      type: number;
      sample_value: string;
    }>;
    tracking_id?: string;
    note?: string;
  }
): Promise<ApiResponseDto<any>> => {
  const response = await apiClient.put<any>(
    `/marketing/zalo/zns/${integrationId}/templates/${templateId}/edit`,
    data
  );
  return response;
};

/**
 * Đồng bộ ZNS templates từ Zalo API sang database
 * API endpoint: POST /v1/marketing/zalo/zns/templates/sync-all
 */
export const syncZNSTemplates = async (): Promise<
  ApiResponseDto<{ syncedCount: number; message: string }>
> => {
  const response = await apiClient.post<{ syncedCount: number; message: string }>(
    '/marketing/zalo/zns/templates/sync-all'
  );
  return response;
};
