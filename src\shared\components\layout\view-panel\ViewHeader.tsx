import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Avatar,
  Dropdown,
  Icon,
  IconCard,
  LanguageFlag,
  ThemeToggle,
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { useLanguage } from '@/shared/contexts/language';
import { useLogoutManager } from '@/shared/hooks';
import { useRPoint } from '@/shared/contexts/useRPoint';
import { useUserPoints } from '@/modules/profile/hooks/useUser';
import { useCartCount } from '@/shared/hooks/useCartCount';
import ViewBreadcrumb from './ViewBreadcrumb';
import { useProfile } from '@/modules/profile/hooks/useProfile';
import { useUpdateTheme } from '@/modules/settings/hooks/useSettings';

interface ViewHeaderProps {
  title: string;
  actions?: React.ReactNode;
}

const ViewHeader = ({ title, actions }: ViewHeaderProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { themeMode, setThemeMode } = useTheme();
  const theme = themeMode === 'custom' ? 'light' : themeMode;

  // Hook để update theme qua API
  const updateThemeMutation = useUpdateTheme();

  // Custom toggle function - chuyển theme ngay lập tức, gọi API trong background
  const handleThemeToggle = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';

    // 1. Cập nhật theme local ngay lập tức
    setThemeMode(newTheme);

    // 2. Gọi API trong background (không đợi kết quả)
    updateThemeMutation.mutate(
      {
        theme: { mode: newTheme },
      },
      {
        onSuccess: () => {
          console.log('Theme updated successfully via API');
        },
        onError: error => {
          console.warn('Failed to update theme via API (but local theme already changed):', error);
          // Theme local đã được cập nhật, không cần rollback
        },
      }
    );
  };

  // Sử dụng try-catch để handle context errors
  let language = 'vi';
  let setLanguage = (_code: 'vi' | 'en' | 'zh') => {};
  let availableLanguages = [
    { code: 'vi', name: 'Tiếng Việt' },
    { code: 'en', name: 'English' },
    { code: 'zh', name: '中文' },
  ];

  try {
    const languageContext = useLanguage();
    language = languageContext.language;
    setLanguage = languageContext.setLanguage;
    availableLanguages = languageContext.availableLanguages;
  } catch (error) {
    console.warn('LanguageContext not available, using fallback values:', error);
  }

  const { performLogout } = useLogoutManager();
  // Kiểm tra xem có đang ở trong module marketplace không
  const isMarketplace = location.pathname.startsWith('/marketplace');

  // Lấy số RPoint của người dùng từ context (fallback)
  const { userRPoints: contextRPoints } = useRPoint();

  // Lấy số RPoint của người dùng từ API
  const { data: apiRPoints } = useUserPoints();

  const { data: profile } = useProfile();

  // Sử dụng điểm từ API nếu có, nếu không thì dùng từ context
  // Đảm bảo userRPoints luôn là một số
  const userRPoints =
    typeof apiRPoints === 'number' && !isNaN(apiRPoints)
      ? apiRPoints
      : typeof contextRPoints === 'number' && !isNaN(contextRPoints)
        ? contextRPoints
        : 0;

  // Lấy số lượng sản phẩm trong giỏ hàng từ API thực
  const { totalItems: cartItemCount } = useCartCount();
  const totalItems = isMarketplace ? cartItemCount : 0;

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle language selection dropdown
      const langDropdown = document.getElementById('language-selection-dropdown');
      const langTrigger = document.getElementById('language-trigger');

      if (
        langDropdown &&
        !langDropdown.contains(event.target as Node) &&
        langTrigger &&
        !langTrigger.contains(event.target as Node)
      ) {
        langDropdown.classList.add('hidden');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sử dụng component LanguageFlag thay vì hàm renderLanguageFlag

  // No separate language dropdown items needed anymore

  // Create profile dropdown items
  const profileItems = [
    {
      id: 'profile',
      label: t('common.profile'),
      onClick: () => navigate('/profile'),
      icon: <Icon name="user" size="sm" />,
    },
    {
      id: 'help',
      label: t('common.help'),
      onClick: () => navigate('/help'),
      icon: <Icon name="help-circle" size="sm" />,
    },
    {
      id: 'language-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name="language" size="sm" className="mr-2" />
            <span className="mr-3">{t('common.language')}</span>
          </div>

          {/* Current language flag */}
          <LanguageFlag code={language as 'vi' | 'en' | 'zh'} isSelected={true} />
        </div>
      ),
      subItems: availableLanguages.map(lang => ({
        id: `lang-${lang.code}`,
        label: (
          <div className="flex items-center">
            <LanguageFlag code={lang.code as 'vi' | 'en' | 'zh'} />
            <span className="ml-2">{lang.name}</span>
            {language === lang.code && (
              <div className="ml-2 text-primary">
                <Icon name="check" size="sm" fill />
              </div>
            )}
          </div>
        ),
        onClick: () => setLanguage(lang.code as 'vi' | 'en' | 'zh'),
      })),
    },
    {
      id: 'theme-divider',
      divider: true,
    },
    {
      id: 'theme-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name={theme === 'light' ? 'sun' : 'moon'} size="sm" className="mr-2" />
            <span className="mr-3">{t('common.theme')}</span>
          </div>

          <ThemeToggle
            theme={theme}
            onToggle={handleThemeToggle}
            lightText={t('common.light')}
            darkText={t('common.dark')}
          />
        </div>
      ),
      onClick: () => {},
    },
    {
      id: 'settings-divider',
      divider: true,
    },
    {
      id: 'settings',
      label: t('common.settings'),
      onClick: () => navigate('/settings'),
      icon: <Icon name="settings" size="sm" />,
    },
    {
      id: 'logout-divider',
      divider: true,
    },
    {
      id: 'logout',
      label: t('common.logout'),
      onClick: performLogout,
      icon: <Icon name="logout" size="sm" />,
    },
  ];

  return (
    <div className="flex items-center justify-between p-3 px-4 w-full max-w-full overflow-hidden">
      <div className="flex items-center overflow-hidden">
        <ViewBreadcrumb title={title} />
        {actions && <div className="ml-4 overflow-hidden">{actions}</div>}
      </div>

      <div className="flex items-center space-x-2 sm:space-x-4 relative z-40">
        {/* Hiển thị RPoint - ẩn trên mobile */}
        <div className="hidden sm:flex items-center px-1 py-1">
          <span className="text-sm font-medium mr-1">
            {userRPoints !== undefined && userRPoints !== null && !Number.isNaN(userRPoints)
              ? new Intl.NumberFormat('vi-VN').format(userRPoints)
              : '0'}
          </span>
          <Icon name="rpoint" size="sm" className="text-red-600 mr-1" />
        </div>
        {/* Icon plus R-Point - ẩn trên mobile */}
        <div
          className="hidden sm:block relative cursor-pointer"
          onClick={e => {
            e.preventDefault();
            navigate('/rpoint/packages');
          }}
        >
          <IconCard
            icon="plus"
            variant="primary"
            size="md"
            className="text-gray-700 dark:text-gray-300 hover:text-primary"
          />
        </div>

        {/* Icon giỏ hàng - ẩn trên mobile */}
        <div
          className="hidden sm:block relative cursor-pointer"
          onClick={e => {
            e.preventDefault();
            // Sử dụng replace: true để thay thế URL hiện tại thay vì thêm vào history stack
            navigate('/marketplace/cart', { replace: true });
          }}
        >
          <Icon
            name="shopping-cart"
            size="md"
            className="text-gray-700 dark:text-gray-300 hover:text-primary"
          />
          {totalItems > 0 && (
            <span className="absolute top-[-4px] left-2 z-50 bg-red-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              {totalItems}
            </span>
          )}
        </div>

        {/* User dropdown */}
        <Dropdown
          trigger={
            <div className="cursor-pointer flex-shrink-0 min-w-[40px]">
              <Avatar
                src={profile?.avatarUrl}
                alt="User"
                size="md"
                status="online"
                className="flex-shrink-0 min-w-[40px] min-h-[40px] w-10 h-10"
              />
            </div>
          }
          items={profileItems}
          placement="bottom-right"
          width="w-56"
        />
      </div>
    </div>
  );
};

export default ViewHeader;
