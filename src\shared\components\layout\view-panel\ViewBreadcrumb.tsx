/* eslint-disable no-duplicate-case */
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Breadcrumb, Icon } from '@/shared/components/common';
import type { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

interface ViewBreadcrumbProps {
  title: string;
  className?: string;
}

const ViewBreadcrumb: React.FC<ViewBreadcrumbProps> = ({ title, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation([
    'common',
    'marketing',
    'sms',
    'admin',
    'marketplace',
    'components',
    'data',
    'business',
    'integration',
    'integrations',
    'tools',
    'user-dataset',
    'model',
    'subscription',
    'rpoint',
    'contract',
    'blog',
    'affiliate',
    'help',
    'settings',
    'profile',
  ]);

  // Tạo breadcrumb items dựa trên đường dẫn hiện tại
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    // Luôn có item Home (hiển thị icon và text "Trang chủ")
    const items: BreadcrumbItem[] = [
      {
        label: t('common:home', 'Trang chủ'),
        path: '/',
        icon: <Icon name="home" size="sm" />,
      },
    ];

    // Nếu không phải trang chủ, thêm trang hiện tại
    if (location.pathname !== '/') {
      // Xử lý các trường hợp đặc biệt
      if (location.pathname.startsWith('/components')) {
        // Xử lý các trang components
        if (location.pathname === '/components') {
          // Trang components chính
          items.push({
            label: t('components:library:title', 'Thư viện Components'),
          });
        } else {
          // Các trang con của components
          items.push({
            label: t('components:library:title', 'Thư viện Components'),
            path: '/components',
            onClick: () => navigate('/components'),
          });

          // Thêm trang hiện tại
          {
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title && title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title || 'Untitled',
            });
          }
        }
      } else {
        // Xử lý các trang khác
        switch (location.pathname) {
          case '/animation-demo':
            items.push({
              label: t('common:animation', 'Hiệu ứng'),
            });
            break;
          case '/responsive-demo':
            items.push({
              label: t('common:componentsText', 'Components'),
            });
            break;
          case '/ai-agents':
            items.push({
              label: t('aiAgents:aiAgents', 'AI Agents'),
            });
            break;
          case '/ai-agents/add':
            items.push({
              label: t('aiAgents:aiAgents', 'AI Agents'),
              path: '/ai-agents',
              onClick: () => navigate('/ai-agents'),
            });
            items.push({
              label: t('aiAgents:agentCreate:title', 'Create Agent'),
            });
            break;
          case '/ai-agents/categories':
            items.push({
              label: t('aiAgents:aiAgents', 'AI Agents'),
              path: '/ai-agents',
              onClick: () => navigate('/ai-agents'),
            });
            items.push({
              label: t('aiAgents:agentCreatePage:title', 'Agent Categories'),
            });
            break;
          case location.pathname.startsWith('/ai-agents/') && location.pathname.includes('/edit')
            ? location.pathname
            : '':
            items.push({
              label: t('aiAgents:aiAgents', 'AI Agents'),
              path: '/ai-agents',
              onClick: () => navigate('/ai-agents'),
            });
            items.push({
              label: t('aiAgents:editAgent:title', 'Edit Agent'),
            });
            break;
          case location.pathname.startsWith('/ai-agents/') &&
          !location.pathname.includes('/edit') &&
          !location.pathname.includes('/add') &&
          !location.pathname.includes('/categories')
            ? location.pathname
            : '':
            items.push({
              label: t('aiAgents:aiAgents', 'AI Agents'),
              path: '/ai-agents',
              onClick: () => navigate('/ai-agents'),
            });
            items.push({
              label: t('aiAgents:common:agentDetail', 'Agent Detail'),
            });
            break;
          case '/settings':
            items.push({
              label: t('settings:title', 'Cài đặt'),
            });
            break;

          // Marketplace module
          case '/marketplace':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            break;
          case '/marketplace/cart':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:cart:title', 'Giỏ hàng'),
            });
            break;
          case location.pathname.startsWith('/marketplace/product/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:product.detail:information', 'Thông tin sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/marketplace/category/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:categories', 'Danh mục'),
            });
            break;
          case '/marketplace/products-for-sale':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:productsForSale:title', 'Sản phẩm đăng bán'),
            });
            break;
          case location.pathname.startsWith('/marketplace/products-for-sale/edit/')
            ? location.pathname
            : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:productsForSale:title', 'Sản phẩm đăng bán'),
              path: '/marketplace/products-for-sale',
              onClick: () => navigate('/marketplace/products-for-sale'),
            });
            items.push({
              label: t('marketplace:productsForSale.edit:title', 'Chỉnh sửa sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/marketplace/products-for-sale/detail/')
            ? location.pathname
            : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:productsForSale:title', 'Sản phẩm đăng bán'),
              path: '/marketplace/products-for-sale',
              onClick: () => navigate('/marketplace/products-for-sale'),
            });
            items.push({
              label: t('marketplace:userProductDetail.title', 'Chi tiết sản phẩm'),
            });
            break;
          case '/marketplace/purchased-products':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:purchasedProducts:title', 'Sản phẩm đã mua'),
            });
            break;
          case location.pathname.startsWith('/business/products/edit/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product:title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form:editTitle', 'Chỉnh sửa sản phẩm'),
            });
            break;

          // Marketing module

          case '/marketing':
            items.push({
              label: t('marketing:title', 'Marketing'),
            });
            break;
          case '/marketing/dashboard':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:dashboard:title', 'Marketing Dashboard'),
            });
            break;
          case '/marketing/audience':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:audience.title', 'Quản lý đối tượng'),
            });
            break;

          // Business module
          case '/business':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
            });
            break;
          case '/business/product':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product:title', 'Sản phẩm'),
            });
            break;
          case '/business/customer':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customer:title', 'Khách hàng'),
            });
            break;
          case '/business/order':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:order:title', 'Đơn hàng'),
            });
            break;
          case location.pathname.startsWith('/business/order/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:order:title', 'Đơn hàng'),
              path: '/business/order',
              onClick: () => navigate('/business/order'),
            });
            items.push({
              label: t('business:order:detail', 'Chi tiết đơn hàng'),
            });
            break;
          case '/business/conversion':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:conversion:title', 'Chuyển đổi'),
            });
            break;
          case '/business/report':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:report:title', 'Báo cáo'),
            });
            break;
          case '/business/inventory':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:inventory:title', 'Quản lý kho'),
            });
            break;
          case '/business/shop':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:shop:title', 'Quản lý cửa hàng'),
            });
            break;
          case '/business/bank-account':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:bankAccount:title', 'Quản lý tài khoản ngân hàng'),
            });
            break;

          case '/business/products/create':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product:title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form:createTitle', 'Tạo sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/business/conversion/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:conversion:title', 'Chuyển đổi'),
              path: '/business/conversion',
              onClick: () => navigate('/business/conversion'),
            });
            items.push({
              label: t('business:conversion.detail:title', 'Chi tiết chuyển đổi'),
            });
            break;
          case location.pathname.startsWith('/business/inventory/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:inventory:title', 'Quản lý kho'),
              path: '/business/inventory',
              onClick: () => navigate('/business/inventory'),
            });
            items.push({
              label: t('business:warehouse:detailTitle', 'Chi tiết kho'),
            });
            break;
          case '/business/virtual-warehouse':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:virtualWarehouse:title', 'Quản lý kho ảo'),
            });
            break;
          case location.pathname.startsWith('/business/warehouse/virtual/')
            ? location.pathname
            : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:virtualWarehouse:title', 'Quản lý kho ảo'),
              path: '/business/virtual-warehouse',
              onClick: () => navigate('/business/virtual-warehouse'),
            });
            items.push({
              label: t('business:warehouse.virtual:detailTitle', 'Chi tiết kho ảo'),
            });
            break;

          // Data module
          case '/data':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
            });
            break;
          case '/data/url':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:url:title', 'Quản lý URL'),
            });
            break;
          case '/data/media':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:media:title', 'Quản lý Media'),
            });
            break;
          case '/data/knowledge-files':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:knowledgeFiles:title', 'Knowledge Files'),
            });
            break;
          case '/data/vector-store':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:vectorStore:title', 'Vector Store'),
            });
            break;
          case '/data/strategy':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:strategy:title', 'Quản lý chiến dịch'),
            });
            break;

          // Admin Data module
          case '/admin/data':
            items.push({
              label: t('admin:data:title', 'Quản lý dữ liệu Admin'),
            });
            break;
          case '/admin/data/media':
            items.push({
              label: t('admin:data:title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.media:title', 'Quản lý Media'),
            });
            break;
          case '/admin/data/url':
            items.push({
              label: t('admin:data:title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.url:title', 'Quản lý URL'),
            });
            break;
          case '/admin/data/knowledge-files':
            items.push({
              label: t('admin:data:title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.knowledgeFiles:title', 'Quản lý Knowledge Files'),
            });
            break;
          case '/admin/data/vector-store':
            items.push({
              label: t('admin:data:title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.vectorStore:title', 'Quản lý Vector Store'),
            });
            break;

          case '/marketing/segment':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:segment.title', 'Quản lý phân đoạn'),
            });
            break;
          case '/marketing/campaign':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu:campaign', 'Chiến dịch'),
            });
            break;
          case '/marketing/tags':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tags.title', 'Quản lý thẻ'),
            });
            break;
          case '/marketing/custom-fields':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:customFields.title', 'Quản lý trường tùy chỉnh'),
            });
            break;
          case '/marketing/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:reports.title', 'Báo cáo'),
            });
            break;
          case '/marketing/template-emails':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:templateEmail.title', 'Quản lý mẫu email'),
            });
            break;

          // SMS Send page
          case '/marketing/sms/send':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:sms.title', 'SMS Marketing'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('marketing:sms.send.title', 'Gửi tin nhắn SMS'),
            });
            break;
          case '/marketing/sms':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
            });
            break;
          case '/marketing/sms/templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('sms:templates:title'),
            });
            break;
          case '/marketing/sms/my-sms-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('sms:smsTemplate.title', 'Mẫu tin nhắn SMS của tôi'),
            });
            break;
          case '/marketing/sms/my-sms-templates/create':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('sms:smsTemplate.title', 'Mẫu tin nhắn SMS của tôi'),
              path: '/marketing/sms/my-sms-templates',
              onClick: () => navigate('/marketing/sms/my-sms-templates'),
            });
            items.push({
              label: t('sms:smsTemplate.form.createTitle', 'Tạo mẫu tin nhắn SMS'),
            });
            break;
          case location.pathname.startsWith('/marketing/sms/my-sms-templates/edit/')
            ? location.pathname
            : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('sms:smsTemplate.title', 'Mẫu tin nhắn SMS của tôi'),
              path: '/marketing/sms/my-sms-templates',
              onClick: () => navigate('/marketing/sms/my-sms-templates'),
            });
            items.push({
              label: t('sms:smsTemplate.form.editTitle', 'Chỉnh sửa mẫu tin nhắn SMS'),
            });
            break;
          case '/marketing/sms/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('sms:modules:title'),
              path: '/marketing/sms',
              onClick: () => navigate('/marketing/sms'),
            });
            items.push({
              label: t('sms:campaigns:title'),
            });
            break;
          case '/marketing/google-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
            });
            break;
          case '/marketing/google-ads/detail':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.detail.title', 'Chi tiết Google Ads'),
            });
            break;
          case '/marketing/google-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads'),
            });
            break;
          case '/marketing/google-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads'),
            });
            break;

          // Google Ads Keywords
          case '/marketing/google-ads/keywords':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.keywords.title', 'Từ khóa Google Ads'),
            });
            break;

          // Google Ads Ads
          case '/marketing/google-ads/ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.ads.title', 'Quảng cáo Google Ads'),
            });
            break;

          // Google Ads Reports
          case '/marketing/google-ads/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.reports.title', 'Báo cáo Google Ads'),
            });
            break;

          // Google Ads Settings
          case '/marketing/google-ads/settings':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.settings.title', 'Cài đặt Google Ads'),
            });
            break;
          case '/marketing/facebook-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
            });
            break;

          // Facebook Ads Detail
          case '/marketing/facebook-ads/detail':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.detail.title', 'Chi tiết Facebook Ads'),
            });
            break;

          // Facebook Ads Accounts
          case '/marketing/facebook-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.accounts.title', 'Tài khoản Facebook Ads'),
            });
            break;

          // Facebook Ads Campaigns
          case '/marketing/facebook-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.campaigns.title', 'Chiến dịch Facebook Ads'),
            });
            break;

          // Create Facebook Campaign
          case '/marketing/facebook-ads/campaigns/create':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.campaigns.title', 'Chiến dịch Facebook Ads'),
              path: '/marketing/facebook-ads/campaigns',
              onClick: () => navigate('/marketing/facebook-ads/campaigns'),
            });
            items.push({
              label: t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch Facebook Ads'),
            });
            break;

          // Facebook Ads Analytics
          case '/marketing/facebook-ads/analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.analytics.title', 'Phân tích Facebook Ads'),
            });
            break;

          // Facebook Auth Callback
          case '/marketing/facebook-ads/auth/callback':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
              path: '/marketing/facebook-ads',
              onClick: () => navigate('/marketing/facebook-ads'),
            });
            items.push({
              label: t('marketing:facebookAds.authCallback.title', 'Xác thực Facebook Ads'),
            });
            break;

          case '/marketing/zalo/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.overview.title', 'Tổng quan Zalo'),
            });
            break;

          // Zalo Ecosystem
          case '/marketing/zalo/ecosystem':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.ecosystem.title', 'Zalo Ecosystem'),
            });
            break;

          // Zalo OA
          case '/marketing/zalo/zalo-oa':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
            });
            break;

          case '/marketing/zalo/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
            });
            break;
          case location.pathname.match(/^\/marketing\/zalo\/accounts\/[^/]+\/followers$/)
            ? location.pathname
            : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
              path: '/marketing/zalo/accounts',
              onClick: () => navigate('/marketing/zalo/accounts'),
            });
            items.push({
              label: t('marketing:zalo.followers.title', 'Quản lý Followers'),
            });
            break;
          case '/marketing/zalo/oa-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.oaTemplates.title', 'Zalo OA Templates'),
            });
            break;
          case '/marketing/zalo/zns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.zns.title', 'ZNS'),
            });
            break;
          case '/marketing/zalo/zns-campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.znsCampaigns.title', 'Chiến dịch ZNS'),
            });
            break;

          // Zalo Personal Campaigns
          case '/marketing/zalo/personal-campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.personalCampaigns.title', 'Chiến dịch Zalo cá nhân'),
            });
            break;

          // Zalo OA Campaigns
          case '/marketing/zalo/oa-campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.oaCampaigns.title', 'Chiến dịch OA'),
            });
            break;

          // ZNS Campaigns List
          case '/marketing/zalo/zns/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zns.campaign.list.title', 'Danh sách chiến dịch ZNS'),
            });
            break;

          // Create ZNS Campaign
          case '/marketing/zalo/zns/campaigns/create':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zns.campaign.list.title', 'Danh sách chiến dịch ZNS'),
              path: '/marketing/zalo/zns/campaigns',
              onClick: () => navigate('/marketing/zalo/zns/campaigns'),
            });
            items.push({
              label: t('marketing:zns.campaign.create.title', 'Tạo chiến dịch ZNS'),
            });
            break;

          // ZNS Templates
          case '/marketing/zalo/zns-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.znsTemplates.title', 'ZNS Templates'),
            });
            break;

          // My Zalo ZNS Templates
          case '/marketing/zalo/my-zalo-zns-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.znsTemplates.title', 'Zalo ZNS Templates của tôi'),
            });
            break;
          case '/marketing/zalo/oa-messages':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.oaMessages.title', 'Tin nhắn OA'),
            });
            break;
          case '/marketing/zalo/followers':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.followers.title', 'Quản lý Followers'),
            });
            break;
          case '/marketing/zalo/groups':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.groups.title', 'Quản lý nhóm'),
            });
            break;
          case '/marketing/zalo/zalo-personal':
          case '/marketing/zalo/personal':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.personalAccount.title', 'Tài khoản cá nhân'),
            });
            break;
          case '/marketing/zalo/zalo-personal/integration':
          case '/marketing/zalo/personal/integration':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.personalAccount.title', 'Tài khoản cá nhân'),
              path: '/marketing/zalo/zalo-personal',
              onClick: () => navigate('/marketing/zalo/zalo-personal'),
            });
            items.push({
              label: t('marketing:zalo.personalAccount.integration.title', 'Tích hợp Zalo cá nhân'),
            });
            break;
          case '/marketing/zalo/analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.analytics.title', 'Phân tích Zalo'),
            });
            break;
          case '/marketing/zalo/automation':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.automation.title', 'Tự động hóa Zalo'),
            });
            break;
          case location.pathname.match(/^\/marketing\/zalo\/oa\/[^/]+$/) ? location.pathname : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.oa.management.title', 'Quản lý Zalo OA'),
            });
            break;
          case '/marketing/zalo/content':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.content.title', 'Nội dung Zalo'),
            });
            break;
          case '/marketing/zalo/articles':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.articles.title', 'Quản lý bài viết Zalo'),
            });
            break;
          case '/marketing/zalo/videos':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.content.title', 'Nội dung Zalo'),
              path: '/marketing/zalo/content',
              onClick: () => navigate('/marketing/zalo/content'),
            });
            items.push({
              label: t('marketing:videos.title', 'Quản lý video'),
            });
            break;
          case '/marketing/email':
          case '/marketing/email/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
            });
            break;
          case '/marketing/email/templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.templates.title', 'Email Templates'),
            });
            break;

          case '/marketing/email/analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.analytics.title', 'Phân tích Email'),
            });
            break;

          // Email Campaigns
          case '/marketing/email/email-campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.campaigns.title', 'Chiến dịch Email'),
            });
            break;

          // Email Automation
          case '/marketing/email/automation':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.automation.title', 'Tự động hóa Email'),
            });
            break;
          case '/marketing/zalo-ads':
          case '/marketing/zalo-ads/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
            });
            break;
          case '/marketing/zalo-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.accounts.title', 'Quản lý tài khoản Zalo Ads'),
            });
            break;
          case '/marketing/zalo-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.campaigns.title', 'Quản lý chiến dịch Zalo Ads'),
            });
            break;
          case '/marketing/zalo-ads/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.reports.title', 'Báo cáo Zalo Ads'),
            });
            break;

          // TikTok Ads Overview
          case '/marketing/tiktok-ads':
          case '/marketing/tiktok-ads/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
            });
            break;

          // TikTok Ads Accounts
          case '/marketing/tiktok-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.accounts.title', 'TikTok Ads Accounts'),
            });
            break;

          // TikTok Ads Campaigns
          case '/marketing/tiktok-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.campaigns.title', 'TikTok Ads Campaigns'),
            });
            break;

          // TikTok Ads Creatives
          case '/marketing/tiktok-ads/creatives':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.creatives.title', 'TikTok Ads Creatives'),
            });
            break;

          // TikTok Ads Audiences
          case '/marketing/tiktok-ads/audiences':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.audiences.title', 'TikTok Ads Audiences'),
            });
            break;

          // TikTok Ads Reports
          case '/marketing/tiktok-ads/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.reports.title', 'TikTok Ads Reports'),
            });
            break;

          // TikTok Ads Settings
          case '/marketing/tiktok-ads/settings':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:tiktokAds.title', 'TikTok Ads'),
              path: '/marketing/tiktok-ads',
              onClick: () => navigate('/marketing/tiktok-ads'),
            });
            items.push({
              label: t('marketing:tiktokAds.settings.title', 'TikTok Ads Settings'),
            });
            break;
          case '/marketing/articles':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:articles.title', 'Quản lý bài viết'),
            });
            break;
          case '/marketing/videos':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            items.push({
              label: t('marketing:zalo.modules.content.title', 'Nội dung Zalo'),
              path: '/marketing/zalo/content',
              onClick: () => navigate('/marketing/zalo/content'),
            });
            items.push({
              label: t('marketing:videos.title', 'Quản lý video'),
            });
            break;

          // Marketing Resources module
          case '/marketing/resources':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
            });
            break;
          case '/marketing/resources/email-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.emailTemplates:title', 'Mẫu Email'),
            });
            break;
          case '/marketing/resources/my-email-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:email.templates.title', 'Email Templates'),
            });
            break;
          case location.pathname.startsWith('/marketing/resources/email-templates/preview/')
            ? location.pathname
            : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.emailTemplates.title', 'Mẫu Email'),
              path: '/marketing/resources/email-templates',
              onClick: () => navigate('/marketing/resources/email-templates'),
            });
            items.push({
              label: t(
                'marketing:resources.emailTemplates.preview.title',
                'Xem trước Email Template'
              ),
            });
            break;
          case '/marketing/resources/zalo-zns-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.zaloZnsTemplates.title', 'Mẫu tin nhắn ZNS'),
            });
            break;
          case '/marketing/resources/sms-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.smsTemplates.title', 'Mẫu tin nhắn SMS'),
            });
            break;
          case '/marketing/resources/zalo-oa-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.zaloOaTemplates.title', 'Mẫu tin nhắn OA'),
            });
            break;
          case '/marketing/resources/zalo-content':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.zaloContent.title', 'Nội dung Zalo của tôi'),
            });
            break;
          case '/marketing/resources/image-editor':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.imageEditor.title', 'Chỉnh sửa ảnh'),
            });
            break;

          // Campaign Analytics
          case '/marketing/resources/campaign-analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.analytics.campaign.title', 'Phân tích chiến dịch'),
            });
            break;

          // ROI Calculator
          case '/marketing/resources/roi-calculator':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:resources.analytics.roi.title', 'Tính ROI'),
            });
            break;

          // My SMS Templates (Resources)
          case '/marketing/resources/my-sms-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:sms.templates.title', 'SMS Templates của tôi'),
            });
            break;

          // My Zalo OA Templates (Resources)
          case '/marketing/resources/my-zalo-oa-templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:zalo.modules.oaTemplates.title', 'Zalo OA Templates của tôi'),
            });
            break;

          // My Zalo Content (Resources)
          case '/marketing/resources/my-zalo-content':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:resources.title', 'Tài nguyên'),
              path: '/marketing/resources',
              onClick: () => navigate('/marketing/resources'),
            });
            items.push({
              label: t('marketing:zalo.modules.content.title', 'Nội dung Zalo của tôi'),
            });
            break;

          // Contract module
          case '/contracts':
            items.push({
              label: t('contract:overview:title', 'Quản lý hợp đồng'),
            });
            break;

          // Integration module
          case '/integrations':
            items.push({
              label: t('integration:title', 'Tích hợp'),
            });
            break;

          case '/integrations/my-integrations':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:myIntegrations:title', 'Tích hợp của tôi'),
            });
            break;
          case '/integrations/bank-accounts':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            break;
          case '/integrations/bank-accounts/mb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:bankAccount.mb:title', 'Quản lý tài khoản MB Bank'),
            });
            break;
          case '/integrations/bank-accounts/acb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:bankAccount.acb:title', 'Quản lý tài khoản ACB Bank'),
            });
            break;
          case '/integrations/bank-accounts/ocb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:bankAccount.ocb:title', ' Quản lý tài khoản OCB Bank'),
            });
            break;
          case '/integrations/bank-accounts/kienlong':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t(
                'integration:bankAccount.kienlong.title',
                ' Quản lý tài khoản Kiên Long Bank'
              ),
            });
            break;

          case '/integrations/gmail':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:gmail.title', 'Kết nối với Gmail'),
            });
            break;
          case '/integrations/gmail/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:gmail.management.title', 'Quản lý Gmail Integrations'),
            });
            break;
          case '/integrations/calendar/google/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:googleCalendar.management.title', 'Quản lý Google Calendar'),
            });
            break;

          //zalo
          case '/integrations/social/zalo-oa/management':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:cards.social.zalo.title', 'Zalo OA/ZNS'),
            });
            break;

          case '/integrations/calendar':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:calendar.title', 'Google Calendar Integration'),
            });
            break;
          case '/integrations/calendar/google':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:calendar.title', 'Google Calendar Integration'),
            });
            break;

          // Email Integration

          case '/integrations/smtp':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:emailSMTP:title', 'Cấu hình Email SMTP'),
            });
            break;
          case '/integrations/email-servers':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:emailSMTP.titleManagement', 'Quản lý Email SMTP'),
            });
            break;
          case '/integrations/email/outlook':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Microsoft Outlook',
            });
            break;
          case '/integrations/email/yahoo':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Yahoo Mail',
            });
            break;
          case '/integrations/email/sendgrid':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'SendGrid',
            });
            break;
          case '/integrations/email/mailchimp':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Mailchimp Transactional',
            });
            break;
          case '/integrations/email/amazon-ses':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Amazon SES',
            });
            break;
          case '/integrations/email/mailgun':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Mailgun',
            });
            break;
          case '/integrations/email/gmail':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'Gmail',
            });
            break;
          case '/integrations/sms/fpt':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:title', 'Quản lý FPT SMS Brandname'),
            });
            break;

          // Banking Integration
          case '/integrations/banking/acb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integrations:banking.acb:title', 'Liên kết tài khoản ACB'),
            });
            break;
          case '/integrations/banking/mb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integrations:banking.mb:title', 'Liên kết tài khoản MB Bank'),
            });
            break;
          case '/integrations/banking/ocb':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integrations:banking.ocb:title', 'Liên kết tài khoản OCB'),
            });
            break;
          case '/integrations/banking/kienlong':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integrations:banking.kienlong:title', 'Liên kết tài khoản Kiên Long Bank'),
            });
            break;

          // SMS Integration
          case '/integrations/sms':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:sms:title', 'Quản lý SMS'),
            });
            break;
          case '/integrations/sms/twilio':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:cards.sms.twilio.title', 'Tích hợp Twilio SMS'),
            });
            break;
          case '/integrations/sms/ftp':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:cards.sms.ftp.title', 'FTP SMS Brandname'),
            });
            break;

          case '/integrations/sms/fpt/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:cards.sms.fpt.title', 'Quản lý FPT SMS Brandname'),
            });
            break;

          case '/integrations/sms/vnpt':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: 'VNPT SMS',
            });
            break;

          // Other Integrations
          case '/integrations/facebook':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:facebook:title', 'Quản lý Facebook'),
            });
            break;
          case '/integrations/website':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:website:title', 'Quản lý Website'),
            });
            break;
          case '/integrations/database':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:database:title', 'Quản lý Database'),
            });
            break;
          case '/integrations/provider-model':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:providerModel:title', 'Quản lý Provider Model'),
            });
            break;

          // AI LLM Integration Pages
          case '/integrations/ai/openai':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.openai:title', 'Tích hợp với OpenAI'),
            });
            break;
          case '/integrations/ai/openai/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:openai:title', 'Quản lý OpenAI Keys'),
            });
            break;
          case '/integrations/ai/anthropic':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.anthropic:title', 'Tích hợp với Anthropic'),
            });
            break;
          case '/integrations/ai/anthropic/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:anthropic:title', 'Quản lý Anthropic Claude Keys'),
            });
            break;
          case '/integrations/ai/google':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.google:title', 'Tích hợp với Google Gemini'),
            });
            break;
          case '/integrations/ai/gemini/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:gemini:title', 'Quản lý Google Gemini Keys'),
            });
            break;
          case '/integrations/ai/deepseek':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.deepseek:title', 'Tích hợp với DeepSeek'),
            });
            break;
          case '/integrations/ai/deepseek/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:deepseek:title', 'Quản lý DeepSeek Keys'),
            });
            break;
          case '/integrations/ai/xai':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.xai:title', 'Tích hợp với XAI'),
            });
            break;
          case '/integrations/ai/xai-grok/manage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:xaiGrok:title', 'Quản lý XAI Grok Keys'),
            });
            break;
          case '/integrations/ai/meta':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:ai.meta:title', 'Tích hợp với Meta Llama'),
            });
            break;
          case '/integrations/google-ads':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:breadcrumb.googleAds', 'Google Ads'),
            });
            break;
          case '/integrations/facebook-ads':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:breadcrumb.facebookAds', 'Facebook Ads'),
            });
            break;
          case '/integrations/google-calendar':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:calendar:title', 'Quản lý Google Calendar'),
            });
            break;
          case '/integrations/shipping':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:shipping:title', 'Quản lý Vận chuyển'),
            });
            break;
          case '/integrations/cloud-storage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:cloudStorage:title', 'Quản lý Cloud Storage'),
            });
            break;
          case '/integrations/enterprise-storage':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:enterpriseStorage:title', 'Quản lý Enterprise Storage'),
            });
            break;
          case '/integrations/external-agents':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:externalAgents:title', 'Quản lý External Agents'),
            });
            break;
          case '/integrations/ghtk':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:cards.shipping.ghtk.breadcrumb', 'Giao hàng tiết kiệm'),
            });
            break;
          case '/integrations/ghn':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:cards.  shipping.ghn.breadcrumb', 'Giao hàng nhanh'),
            });
            break;
          case '/integrations/ahamove':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });

            items.push({
              label: t('integration:cards.shipping.ahamove.breadcrumb', 'Ahamove'),
            });
            break;

          // Social Integration
          case '/integrations/social/zalo-oa':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('marketing:zalo:accounts:connect:page:title'),
            });
            break;

          // Bank Accounts Management
          case '/user/bank-accounts':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:bankAccounts:title', 'Quản lý Tài khoản ngân hàng'),
            });
            break;

          case '/profile':
            items.push({
              label: t('common:profile', 'Thông tin cá nhân'),
            });
            break;

          // Admin Integration module
          case '/admin/integration':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
            });
            break;
          case '/admin/integration/email':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.email:title', 'Quản lý Email Server'),
            });
            break;
          case '/admin/integration/provider-model':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.providerModel:title', 'Quản lý Provider Model'),
            });
            break;
          case '/admin/integration/sms':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.sms:title', 'Quản lý SMS Server'),
            });
            break;
          case '/admin/integration/payment':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.payment:title', 'Quản lý Payment Gateway'),
            });
            break;
          case '/admin/integration/social':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.social:title', 'Quản lý Social Media'),
            });
            break;
          case '/admin/integration/api-keys':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.apiKeys:title', 'Quản lý API Keys'),
            });
            break;
          case '/admin/integration/webhooks':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration:title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.webhooks:title', 'Quản lý Webhooks'),
            });
            break;

          // Admin Tool module
          case '/admin/tools':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin-tool:title', 'Quản lý Tools'),
            });
            break;
          case '/admin/tools/list':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin-tool:title', 'Quản lý Tools'),
              path: '/admin/tools',
              onClick: () => navigate('/admin/tools'),
            });
            items.push({
              label: t('admin-tool:tools', 'Danh sách Tools'),
            });
            break;
          case '/admin/tools/trash':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:tool:title', 'Quản lý Tools'),
              path: '/admin/tools',
              onClick: () => navigate('/admin/tools'),
            });
            items.push({
              label: t('admin:tool.trash:title', 'Danh sách Tools đã xóa'),
            });
            break;

          // User Tools module
          case '/tools':
            items.push({
              label: t('tools:title', 'Công cụ'),
            });
            break;
          case '/tools/list':
            items.push({
              label: t('tools:title', 'Công cụ'),
              path: '/tools',
              onClick: () => navigate('/tools'),
            });
            items.push({
              label: t('tools:list.title', 'Danh sách Tools'),
            });
            break;
          case location.pathname.startsWith('/tools/') ? location.pathname : '':
            // Xử lý các trang con của tools (tool detail, tool versions, etc.)
            items.push({
              label: t('tools:title', 'Công cụ'),
              path: '/tools',
              onClick: () => navigate('/tools'),
            });
            // Thêm trang hiện tại dựa trên title
            {
              const isTranslationKey = title && title.includes(':') && !title.includes(' ');
              items.push({
                label: isTranslationKey ? t(title) : title || 'Untitled',
              });
            }
            break;

          case '/tools/mcp': // Thêm trường hợp này để xử lý trang MCP
            items.push({
              label: t('tools:title', 'Công cụ'),
              path: '/tools',
              onClick: () => navigate('/tools'),
            });
            // Debug log để kiểm tra translation
            console.log('MCP title translation:', t('tools:mcp.title', 'Máy chủ MCP'));
            items.push({
              label: t('tools:mcp.title', 'Máy chủ MCP'),
            });
            break;

          // Admin Business module
          case '/admin/business':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
            });
            break;
          case '/admin/business/product':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:product:title', 'Quản lý sản phẩm'),
            });
            break;
          case '/admin/business/conversion':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:conversion:title', 'Quản lý chuyển đổi'),
            });
            break;
          case '/admin/business/order':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:order:title', 'Quản lý đơn hàng'),
            });
            break;
          case '/admin/business/warehouse':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:warehouse:title', 'Quản lý kho'),
            });
            break;
          case '/admin/business/custom-field':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:customField:title', 'Trường tùy chỉnh'),
            });
            break;
          case '/admin/business/warehouse-custom-field':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:warehouseCustomField:title', 'Trường tùy chỉnh kho'),
            });
            break;
          case '/admin/business/file':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:file:title', 'Quản lý file'),
            });
            break;
          case '/admin/business/folder':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:folder:title', 'Quản lý thư mục'),
            });
            break;

          case '/business/custom-field':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customField:title', 'Trường tùy chỉnh'),
            });
            break;
          case '/business/custom-group-form':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customGroupForm:title', 'Nhóm trường tùy chỉnh'),
            });
            break;

          // User Dataset module
          case '/user-dataset':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
            });
            break;
          case '/user-dataset/data-fine-tune':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:dataFineTune:title', 'Dataset Fine-tune'),
            });
            break;
          case '/user-dataset/create-openai':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:createDataset.openai:title', 'Tạo Dataset OpenAI'),
            });
            break;
          case '/user-dataset/create-google':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:createDataset.google:title', 'Tạo Dataset Google'),
            });
            break;
          // Model module
          case '/model':
            items.push({
              label: t('model:title', 'Quản lý Model'),
            });
            break;
          case '/model/system-models':
            items.push({
              label: t('model:title', 'Quản lý Model'),
              path: '/model',
              onClick: () => navigate('/model'),
            });
            items.push({
              label: t('model:systemModels:title', 'Mô hình hệ thống'),
            });
            break;
          case '/model/user-models':
            items.push({
              label: t('model:title', 'Quản lý Model'),
              path: '/model',
              onClick: () => navigate('/model'),
            });
            items.push({
              label: t('model:userModels:title', 'Mô hình người dùng'),
            });
            break;
          case '/model/fine-tuning':
            items.push({
              label: t('model:title', 'Quản lý Model'),
              path: '/model',
              onClick: () => navigate('/model'),
            });
            items.push({
              label: t('model:fineTuning:title', 'Tinh chỉnh các mô hình'),
            });
            break;

          // Subscription module

          case '/subscription/packages':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('subscription:title', 'Gói Dịch Vụ RedAI'),
            });
            break;
          case '/subscription/order':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('subscription:title', 'Gói Dịch Vụ RedAI'),
              path: '/subscription/packages',
              onClick: () => navigate('/subscription/packages'),
            });
            items.push({
              label: t('subscription:order:title', 'Đặt hàng'),
            });
            break;
          case '/subscription/payment-success':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('subscription:title', 'Gói Dịch Vụ RedAI'),
              path: '/subscription/packages',
              onClick: () => navigate('/subscription/packages'),
            });
            items.push({
              label: t('subscription:payment:success', 'Thanh toán thành công'),
            });
            break;
          case location.pathname.startsWith('/subscription/payment/bank-transfer/')
            ? location.pathname
            : '':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('subscription:title', 'Gói Dịch Vụ RedAI'),
              path: '/subscription/packages',
              onClick: () => navigate('/subscription/packages'),
            });
            items.push({
              label: t('subscription:order:title', 'Đặt hàng'),
              path: '/subscription/order',
              onClick: () => navigate('/subscription/order'),
            });
            items.push({
              label: t('subscription:payment.bankTransfer:title', 'Chuyển khoản ngân hàng'),
            });
            break;
          case '/subscription/pricing-showcase':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('subscription:title', 'Gói Dịch Vụ RedAI'),
              path: '/subscription/packages',
              onClick: () => navigate('/subscription/packages'),
            });
            items.push({
              label: 'Pricing Cards Showcase',
            });
            break;

          // Admin Subscription module
          case '/admin/subscription':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('subscription:title', 'Quản lý gói dịch vụ'),
            });
            break;
          case '/admin/subscription/plans':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('subscription:title', 'Quản lý gói dịch vụ'),
              path: '/admin/subscription',
              onClick: () => navigate('/admin/subscription'),
            });
            items.push({
              label: t('subscription:plans:title', 'Quản lý gói'),
            });
            break;
          case '/admin/subscription/plan-pricing':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('subscription:title', 'Quản lý gói dịch vụ'),
              path: '/admin/subscription',
              onClick: () => navigate('/admin/subscription'),
            });
            items.push({
              label: t('subscription:planPricing:title', 'Quản lý giá gói'),
            });
            break;

          // Service Packages module
          case '/service-packages':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
            });
            break;

          // R-Point module
          case '/rpoint/packages':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('rpoint:breadcrumb:packages', 'Gói R-Point'),
            });
            break;
          case '/rpoint/order':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('rpoint:breadcrumb:packages', 'Gói R-Point'),
              path: '/rpoint/packages',
              onClick: () => navigate('/rpoint/packages'),
            });
            items.push({
              label: t('rpoint:breadcrumb:order', 'Thông tin đơn hàng'),
            });
            break;
          case location.pathname.startsWith('/rpoint/payment/') ? location.pathname : '':
            items.push({
              label: t('common:servicePackages.title', 'Gói dịch vụ'),
              path: '/service-packages',
              onClick: () => navigate('/service-packages'),
            });
            items.push({
              label: t('rpoint:breadcrumb:packages', 'Gói R-Point'),
              path: '/rpoint/packages',
              onClick: () => navigate('/rpoint/packages'),
            });
            items.push({
              label: t('rpoint:breadcrumb:order', 'Thông tin đơn hàng'),
              path: '/rpoint/order',
              onClick: () => navigate('/rpoint/order'),
            });
            items.push({
              label: t('rpoint:breadcrumb:payment', 'Thanh toán'),
            });
            break;

          // Blog module
          case '/blog':
            items.push({
              label: t('blog:title', 'Blog'),
            });
            break;
          case '/blog/list':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            items.push({
              label: t('blog:title', 'Blog'),
            });
            break;
          case '/blog/purchased':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            items.push({
              label: t('blog:purchasedBlogs', 'Purchased Blogs'),
            });
            break;
          case '/blog/personal':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            items.push({
              label: t('blog:myBlogs', 'My Blogs'),
            });
            break;
          case '/blog/create':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            items.push({
              label: t('blog:createBlog', 'Create Blog'),
            });
            break;
          case location.pathname.startsWith('/blog/detail/') ? location.pathname : '':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            items.push({
              label: t('blog:detail.title', 'Blog Detail'),
            });
            break;
          case location.pathname.startsWith('/blog/tag/') ? location.pathname : '':
            items.push({
              label: t('blog:title', 'Blog'),
              path: '/blog',
              onClick: () => navigate('/blog'),
            });
            // Thêm trang hiện tại dựa trên title
            {
              const isTranslationKey = title && title.includes(':') && !title.includes(' ');
              items.push({
                label: isTranslationKey ? t(title) : title || 'Untitled',
              });
            }
            break;

          // Help Center module
          case '/help':
            items.push({
              label: t('help:title', 'Trung tâm hỗ trợ'),
            });
            break;
          case '/help/my-requests':
            items.push({
              label: t('help:title', 'Trung tâm hỗ trợ'),
              path: '/help',
            });
            items.push({
              label: t('help:myRequests', 'Yêu cầu hỗ trợ của tôi'),
            });
            break;

          case location.pathname.startsWith('/profile/') ? location.pathname : '':
            items.push({
              label: 'Hồ sơ',
              path: '/profile',
              onClick: () => navigate('/profile'),
            });
            // Thêm trang hiện tại dựa trên title
            {
              const isTranslationKey = title && title.includes(':') && !title.includes(' ');
              items.push({
                label: isTranslationKey ? t(title) : title || 'Untitled',
              });
            }
            break;

          // Contract pages (now accessed from profile)
          case '/contract/principle':
            items.push({
              label: 'Hồ sơ',
              path: '/profile',
              onClick: () => navigate('/profile'),
            });
            items.push({
              label: t('contract:overview.principleContract.title', 'Hợp đồng nguyên tắc'),
            });
            break;
          case '/contract-affiliate':
            items.push({
              label: 'Hồ sơ',
              path: '/profile',
              onClick: () => navigate('/profile'),
            });
            items.push({
              label: t('contract:overview.affiliateContract.title', 'Hợp đồng Affiliate'),
            });
            break;

          default: {
            // Nếu không có xử lý đặc biệt, sử dụng title được truyền vào
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title && title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title || 'Untitled',
            });
          }
        }
      }
    }

    return items;
  };

  return (
    <div className="flex items-center overflow-hidden">
      <Breadcrumb items={generateBreadcrumbItems()} className={`text-sm ${className} truncate`} />
      {/* Hiệu ứng ánh sáng nhẹ */}
      <div className="absolute w-8 h-8 bg-primary opacity-10 rounded-full blur-xl -z-10 animate-pulse-slow"></div>
    </div>
  );
};

export default ViewBreadcrumb;
