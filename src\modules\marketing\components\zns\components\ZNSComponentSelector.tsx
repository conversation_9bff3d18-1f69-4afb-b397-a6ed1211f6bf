import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typo<PERSON>, Button, Icon } from '@/shared/components/common';

// Import ZNS Components
import ZNSLogoComponent from './ZNSLogoComponent';
import ZNSImagesComponent from './ZNSImagesComponent';
import ZNSTitleComponent from './ZNSTitleComponent';
import ZNSParagraphComponent from './ZNSParagraphComponent';
import ZNSTableComponent from './ZNSTableComponent';
import ZNSButtonsComponent from './ZNSButtonsComponent';
import ZNSVoucherComponent from './ZNSVoucherComponent';
import ZNSPaymentComponent from './ZNSPaymentComponent';
import ZNSOTPComponent from './ZNSOTPComponent';
import ZNSRatingComponent from './ZNSRatingComponent';

// Import utilities
import {
  getTemplateConfig,
  sortComponentsByOrder,
  getRequiredComponents,
} from '../utils/znsComponentOrder';

interface ZNSComponentSelectorProps {
  onComponentsChange: (components: any[]) => void;
  initialComponents?: any[];
  templateType?: string;
  selectedComponents?: any[]; // Để hiển thị trạng thái từ parent
  integrationId: string; // Required for API calls
  onValidationChange?: (isValid: boolean, errors: string[]) => void; // Callback để thông báo validation status
}

interface ComponentType {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  section: 'header' | 'body' | 'footer';
}

interface ComponentLimits {
  min: number;
  max: number;
  mutuallyExclusive?: string[]; // Các component không thể cùng tồn tại
}

const ZNSComponentSelector: React.FC<ZNSComponentSelectorProps> = ({
  onComponentsChange,
  initialComponents = [],
  templateType,
  integrationId,
  onValidationChange,
}) => {
  const { t } = useTranslation(['marketing']);
  const [selectedComponents, setSelectedComponents] = useState<any[]>([]);
  const [validationState, setValidationState] = useState<{ isValid: boolean; errors: string[] }>({
    isValid: true,
    errors: [],
  });
  const isInitializedRef = useRef(false);
  const lastTemplateTypeRef = useRef(templateType);
  const initialComponentsRef = useRef<any[]>([]);
  const onComponentsChangeRef = useRef(onComponentsChange);

  // Update ref when callback changes
  useEffect(() => {
    onComponentsChangeRef.current = onComponentsChange;
  }, [onComponentsChange]);

  // Initialize components only once
  useEffect(() => {
    if (!templateType) return;

    // Reset khi templateType thay đổi
    if (templateType !== lastTemplateTypeRef.current) {
      console.log('🔄 [ZNSComponentSelector] Template type changed, resetting:', {
        from: lastTemplateTypeRef.current,
        to: templateType,
      });

      isInitializedRef.current = false;
      lastTemplateTypeRef.current = templateType;
      initialComponentsRef.current = [];
      setSelectedComponents([]);
    }

    // Chỉ khởi tạo một lần khi chưa initialized
    if (!isInitializedRef.current) {
      // Nếu có initialComponents và chưa được lưu trong ref
      if (
        initialComponents &&
        initialComponents.length > 0 &&
        JSON.stringify(initialComponents) !== JSON.stringify(initialComponentsRef.current)
      ) {
        console.log('🔄 [ZNSComponentSelector] Using initialComponents:', initialComponents);
        initialComponentsRef.current = [...initialComponents];
        setSelectedComponents([...initialComponents]);
        onComponentsChangeRef.current([...initialComponents]);
        isInitializedRef.current = true;
        return;
      }

      // Tạo default components nếu không có initialComponents
      const requiredComponents = getRequiredComponents(templateType);
      const defaultComponents = requiredComponents.map(type => ({
        id: `${type.toLowerCase()}-${Date.now()}`,
        type,
        data: getDefaultDataForComponent(type),
      }));

      const sortedComponents = sortComponentsByOrder(defaultComponents, templateType);
      console.log('🔄 [ZNSComponentSelector] Creating default components:', sortedComponents);
      setSelectedComponents(sortedComponents);
      onComponentsChangeRef.current(sortedComponents);
      isInitializedRef.current = true;
    }
  }, [templateType]); // Chỉ phụ thuộc vào templateType, onComponentsChange dùng ref

  // Kiểm tra validation theo quy định Zalo
  const validateComponents = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const limits = getComponentLimits();

    // Kiểm tra từng component type theo giới hạn
    Object.entries(limits).forEach(([componentType, limit]) => {
      const currentCount = selectedComponents.filter(comp => comp.type === componentType).length;

      // Kiểm tra minimum requirement
      if (currentCount < limit.min) {
        const componentName = getComponentDisplayName(componentType);
        if (limit.min === 1) {
          errors.push(
            t('marketing:zalo.zns.componentSelector.validation.required', {
              component: componentName,
            })
          );
        } else {
          errors.push(
            t('marketing:zalo.zns.componentSelector.validation.minRequired', {
              component: componentName,
              min: limit.min,
              current: currentCount,
            })
          );
        }
      }

      // Kiểm tra maximum limit
      if (currentCount > limit.max) {
        const componentName = getComponentDisplayName(componentType);
        errors.push(
          t('marketing:zalo.zns.componentSelector.validation.maxExceeded', {
            component: componentName,
            max: limit.max,
            current: currentCount,
          })
        );
      }
    });

    // Kiểm tra mutual exclusion (Logo và Image không thể cùng tồn tại)
    const logoCount = selectedComponents.filter(comp => comp.type === 'LOGO').length;
    const imageCount = selectedComponents.filter(comp => comp.type === 'IMAGES').length;

    if (logoCount > 0 && imageCount > 0) {
      errors.push(t('marketing:zalo.zns.componentSelector.validation.headerLogoImageExclusive'));
    }

    // Kiểm tra đặc biệt cho template type 5 (Voucher): phải có ít nhất 1 trong 2 (Logo hoặc Image)
    if (templateType === '5') {
      if (logoCount === 0 && imageCount === 0) {
        errors.push(t('marketing:zalo.zns.componentSelector.validation.voucherHeaderRequired'));
      }
    }

    return { isValid: errors.length === 0, errors };
  }, [selectedComponents, templateType, t]);

  // Validation effect - chỉ chạy khi selectedComponents hoặc templateType thay đổi
  useEffect(() => {
    if (templateType && isInitializedRef.current) {
      const validation = validateComponents();

      // Chỉ update state nếu có thay đổi thực sự
      setValidationState(prevState => {
        if (
          prevState.isValid !== validation.isValid ||
          JSON.stringify(prevState.errors) !== JSON.stringify(validation.errors)
        ) {
          return validation;
        }
        return prevState;
      });

      // Notify parent if callback exists
      if (onValidationChange) {
        onValidationChange(validation.isValid, validation.errors);
      }
    }
  }, [templateType, validateComponents, onValidationChange]); // Sử dụng stable validateComponents

  const getDefaultDataForComponent = (type: string) => {
    switch (type) {
      case 'TITLE':
        return { content: t('marketing:zalo.zns.componentSelector.defaultContent.title') };
      case 'PARAGRAPH':
        return { content: t('marketing:zalo.zns.componentSelector.defaultContent.paragraph') };
      case 'IMAGES':
        // Không có dữ liệu mẫu - để trống để user upload
        return {
          IMAGES: {
            items: [],
          },
          images: [], // For preview
        };
      case 'LOGO':
        // Không có dữ liệu mẫu - để trống để user upload
        return {
          LOGO: {
            light: '',
            dark: '',
          },
          images: [], // For preview
        };
      case 'OTP':
        return { code: '<otp>' };
      case 'VOUCHER':
        return {
          VOUCHER: {
            name: '',
            condition: '',
            start_date: '',
            end_date: '',
            voucher_code: 'ABC123',
            display_code: '2',
          },
          voucher: {
            name: '',
            condition: '',
            start_date: '',
            end_date: '',
            voucher_code: 'ABC123',
            display_code: '2',
          },
        };
      case 'PAYMENT':
        return {
          PAYMENT: {
            bank_code: '',
            account_name: '',
            bank_account: '',
            amount: '',
            note: '',
          },
          payment: {
            bankName: '',
            accountName: '',
            accountNumber: '',
            amount: '',
            note: '',
          },
        };
      case 'RATING':
        return {
          RATING: {
            items: [
              {
                star: 1,
                title: 'Rất không hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 2,
                title: 'Không hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 3,
                title: 'Bình thường',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 4,
                title: 'Hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 5,
                title: 'Rất hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
            ],
          },
          rating: {
            items: [
              {
                star: 1,
                title: 'Rất không hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 2,
                title: 'Không hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 3,
                title: 'Bình thường',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 4,
                title: 'Hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
              {
                star: 5,
                title: 'Rất hài lòng',
                question: '',
                answers: [],
                thanks: '',
                description: '',
              },
            ],
          },
        };
      case 'TABLE':
        return {
          TABLE: {
            header: ['Sản phẩm', 'Số lượng', 'Giá'],
            rows: [['Sản phẩm mẫu', '1', '100,000 VND']],
          },
        };
      case 'BUTTONS':
        return {
          items: [
            {
              type: 1,
              title: 'Tìm hiểu thêm',
              content: 'https://example.com',
            },
          ],
        };
      default:
        return {};
    }
  };

  const allComponents: ComponentType[] = [
    {
      id: 'images',
      name: t('marketing:zalo.zns.componentSelector.components.images.name'),
      description: t('marketing:zalo.zns.componentSelector.components.images.description'),
      icon: 'image',
      color: 'text-blue-500',
      type: 'IMAGES',
      section: 'header',
    },
    {
      id: 'logo',
      name: 'Logo',
      description: 'Logo thương hiệu',
      icon: 'image',
      color: 'text-purple-500',
      type: 'LOGO',
      section: 'header',
    },
    {
      id: 'title',
      name: t('marketing:zalo.zns.components.title.label'),
      description: t('marketing:zalo.zns.components.title.help'),
      icon: 'type',
      color: 'text-green-500',
      type: 'TITLE',
      section: 'body',
    },
    {
      id: 'paragraph',
      name: t('marketing:zalo.zns.componentSelector.components.paragraph.name'),
      description: t('marketing:zalo.zns.componentSelector.components.paragraph.description'),
      icon: 'align-left',
      color: 'text-orange-500',
      type: 'PARAGRAPH',
      section: 'body',
    },
    {
      id: 'otp',
      name: t('marketing:zalo.zns.componentSelector.components.otp.name'),
      description: t('marketing:zalo.zns.componentSelector.components.otp.description'),
      icon: 'key',
      color: 'text-red-500',
      type: 'OTP',
      section: 'body',
    },
    {
      id: 'table',
      name: t('marketing:zalo.zns.componentSelector.components.table.name'),
      description: t('marketing:zalo.zns.componentSelector.components.table.description'),
      icon: 'table',
      color: 'text-indigo-500',
      type: 'TABLE',
      section: 'body',
    },
    {
      id: 'voucher',
      name: t('marketing:zalo.zns.componentSelector.components.voucher.name'),
      description: t('marketing:zalo.zns.componentSelector.components.voucher.description'),
      icon: 'gift',
      color: 'text-pink-500',
      type: 'VOUCHER',
      section: 'body',
    },
    {
      id: 'payment',
      name: t('marketing:zalo.zns.componentSelector.components.payment.name'),
      description: t('marketing:zalo.zns.componentSelector.components.payment.description'),
      icon: 'credit-card',
      color: 'text-yellow-500',
      type: 'PAYMENT',
      section: 'body',
    },
    {
      id: 'rating',
      name: 'Rating',
      description: t('marketing:zalo.zns.componentSelector.components.rating.description'),
      icon: 'star',
      color: 'text-yellow-600',
      type: 'RATING',
      section: 'body',
    },
    {
      id: 'buttons',
      name: t('marketing:zalo.zns.componentSelector.components.buttons.name'),
      description: t('marketing:zalo.zns.componentSelector.components.buttons.description'),
      icon: 'mouse-pointer',
      color: 'text-teal-500',
      type: 'BUTTONS',
      section: 'footer',
    },
  ];

  // Quy định giới hạn theo Zalo ZNS
  const getComponentLimits = (): Record<string, ComponentLimits> => {
    // Giới hạn khác nhau theo template type
    const baseLimits = {
      // Header: Chỉ chứa 1 trong 2 components (Logo hoặc Image)
      LOGO: { min: 0, max: 1, mutuallyExclusive: ['IMAGES'] },
      IMAGES: { min: 0, max: 1, mutuallyExclusive: ['LOGO'] },

      // Body: 1 tiêu đề, 0-4 đoạn văn, 0-1 bảng
      TITLE: { min: 0, max: 1 },
      PARAGRAPH: { min: 0, max: 4 },
      TABLE: { min: 0, max: 1 },
      OTP: { min: 0, max: 1 },
      VOUCHER: { min: 0, max: 1 },
      PAYMENT: { min: 0, max: 1 },
      RATING: { min: 0, max: 1 },

      // Footer: 0-2 buttons, nếu có image thì phải có ít nhất 1 button
      BUTTONS: { min: 0, max: 2 },
    };

    // Điều chỉnh theo template type
    if (templateType === '2') {
      // ZNS xác thực
      return {
        ...baseLimits,
        LOGO: { min: 1, max: 1 }, // Bắt buộc có logo
        OTP: { min: 1, max: 1 }, // Bắt buộc có OTP
        PARAGRAPH: { min: 1, max: 1 }, // Bắt buộc có 1 paragraph
      };
    }

    // Quy định riêng cho ZNS đánh giá dịch vụ
    if (templateType === '3') {
      return {
        ...baseLimits,
        LOGO: { min: 1, max: 1 }, // Bắt buộc có logo
        TITLE: { min: 1, max: 1 }, // Bắt buộc có tiêu đề
        PARAGRAPH: { min: 0, max: 1 }, // Tùy chọn có paragraph
        RATING: { min: 1, max: 1 }, // Bắt buộc có rating
        BUTTONS: { min: 0, max: 2 }, // Tùy chọn có buttons
      };
    }

    // Quy định riêng cho ZNS yêu cầu thanh toán
    if (templateType === '4') {
      return {
        ...baseLimits,
        LOGO: { min: 1, max: 1 }, // Bắt buộc có logo
        TITLE: { min: 1, max: 1 }, // Bắt buộc có tiêu đề
        PARAGRAPH: { min: 0, max: 4 }, // Tùy chọn có paragraph
        TABLE: { min: 0, max: 1 }, // Tùy chọn có table
        PAYMENT: { min: 1, max: 1 }, // Bắt buộc có payment
        BUTTONS: { min: 0, max: 2 }, // Tùy chọn có buttons
      };
    }

    // Quy định riêng cho ZNS Voucher
    if (templateType === '5') {
      return {
        ...baseLimits,
        LOGO: { min: 1, max: 1, mutuallyExclusive: ['IMAGES'] }, // Logo hoặc Image
        IMAGES: { min: 1, max: 1, mutuallyExclusive: ['LOGO'] }, // Logo hoặc Image
        TITLE: { min: 1, max: 1 }, // Bắt buộc có tiêu đề
        PARAGRAPH: { min: 0, max: 4 }, // Tùy chọn có paragraph
        TABLE: { min: 0, max: 1 }, // Tùy chọn có table
        VOUCHER: { min: 1, max: 1 }, // Bắt buộc có voucher
        BUTTONS: { min: 0, max: 2 }, // Tùy chọn có buttons, nếu có image thì cần ít nhất 1
      };
    }

    return baseLimits;
  };

  // Lọc components theo template type
  const getAvailableComponents = (): ComponentType[] => {
    switch (templateType) {
      case '1': // ZNS tùy chỉnh
        return allComponents.filter(comp =>
          ['IMAGES', 'LOGO', 'TITLE', 'PARAGRAPH', 'TABLE', 'BUTTONS'].includes(comp.type)
        );
      case '2': // ZNS xác thực
        return allComponents.filter(comp => ['LOGO', 'OTP', 'PARAGRAPH'].includes(comp.type));
      case '3': // ZNS đánh giá dịch vụ
        return allComponents.filter(comp =>
          ['LOGO', 'TITLE', 'PARAGRAPH', 'RATING', 'BUTTONS'].includes(comp.type)
        );
      case '4': // ZNS yêu cầu thanh toán
        return allComponents.filter(comp =>
          ['LOGO', 'TITLE', 'PARAGRAPH', 'PAYMENT', 'BUTTONS'].includes(comp.type)
        );
      case '5': // ZNS Voucher
        return allComponents.filter(comp =>
          ['LOGO', 'IMAGES', 'TITLE', 'PARAGRAPH', 'TABLE', 'VOUCHER', 'BUTTONS'].includes(
            comp.type
          )
        );
      default:
        return [];
    }
  };

  const availableComponents = getAvailableComponents();
  const componentLimits = getComponentLimits();

  // Kiểm tra component có thể xóa không
  const canRemoveComponent = useCallback(
    (componentType: string): boolean => {
      const limits = componentLimits[componentType];
      const currentCount = selectedComponents.filter(comp => comp.type === componentType).length;

      // Nếu min >= 1 và hiện tại chỉ có 1 component thì không được xóa
      return !(limits?.min >= 1 && currentCount <= limits.min);
    },
    [selectedComponents, componentLimits]
  );

  // Kiểm tra xem có thể thêm component không
  const canAddComponent = useCallback(
    (componentType: string): boolean => {
      const currentCount = selectedComponents.filter(comp => comp.type === componentType).length;
      const limit = componentLimits[componentType];

      if (!limit || currentCount >= limit.max) {
        return false;
      }

      // Kiểm tra mutual exclusion (ví dụ: Logo và Image không thể cùng tồn tại)
      if (limit.mutuallyExclusive) {
        const hasExclusiveComponent = selectedComponents.some(comp =>
          limit.mutuallyExclusive!.includes(comp.type)
        );
        if (hasExclusiveComponent) {
          return false;
        }
      }

      return true;
    },
    [selectedComponents, componentLimits]
  );

  const handleAddComponent = useCallback(
    (componentType: ComponentType | string) => {
      const type = typeof componentType === 'string' ? componentType : componentType.type;

      if (!canAddComponent(type)) {
        return;
      }

      // Tạo data mặc định
      let defaultData = getDefaultDataForComponent(type);

      // Override cho ZNS xác thực
      if (templateType === '2' && type === 'PARAGRAPH') {
        defaultData = {
          content:
            'Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút.',
        };
      }

      const newComponent = {
        id: `${type.toLowerCase()}_${Date.now()}`,
        type: type,
        data: defaultData,
      };

      setSelectedComponents(prevComponents => {
        const updatedComponents = [...prevComponents, newComponent];
        onComponentsChangeRef.current(updatedComponents);
        return updatedComponents;
      });
    },
    [templateType]
  );

  // Nhóm components theo section
  const getComponentsBySection = () => {
    const headerComponents = availableComponents.filter(comp => comp.section === 'header');
    const bodyComponents = availableComponents.filter(comp => comp.section === 'body');
    const footerComponents = availableComponents.filter(comp => comp.section === 'footer');

    return { headerComponents, bodyComponents, footerComponents };
  };

  // Stable update handler using useCallback
  const handleComponentUpdate = useCallback((componentId: string, newData: any) => {
    console.log('🔄 [ZNSComponentSelector] handleUpdate called:', {
      componentId,
      newData,
    });

    setSelectedComponents(prevComponents => {
      const updatedComponents = prevComponents.map(comp =>
        comp.id === componentId ? { ...comp, data: newData } : comp
      );

      console.log('🔄 [ZNSComponentSelector] Updated components:', updatedComponents);

      // Call onComponentsChange with updated components
      onComponentsChangeRef.current(updatedComponents);

      return updatedComponents;
    });
  }, []);

  // Stable remove handler using useCallback
  const handleComponentRemove = useCallback((componentId: string, componentType: string) => {
    // Kiểm tra xem component có thể xóa không
    if (!canRemoveComponent(componentType)) {
      return; // Không cho phép xóa component bắt buộc
    }

    setSelectedComponents(prevComponents => {
      let updatedComponents = prevComponents.filter(comp => comp.id !== componentId);

      // Logic thông minh: Nếu xóa LOGO thì tự động xóa IMAGES (vì IMAGES chỉ hiện khi có LOGO)
      if (componentType === 'LOGO') {
        updatedComponents = updatedComponents.filter(comp => comp.type !== 'IMAGES');
      }

      onComponentsChangeRef.current(updatedComponents);
      return updatedComponents;
    });
  }, []);

  // Render inline component using existing ZNS components
  const renderInlineComponent = (component: any) => {
    const handleRemove = () => handleComponentRemove(component.id, component.type);
    const handleUpdate = (newData: any) => handleComponentUpdate(component.id, newData);

    switch (component.type) {
      case 'IMAGES':
        return (
          <ZNSImagesComponent
            onAdd={handleUpdate}
            onRemove={handleRemove}
            data={component.data}
            integrationId={integrationId}
          />
        );

      case 'LOGO':
        return (
          <ZNSLogoComponent
            onAdd={handleUpdate}
            onRemove={handleRemove}
            data={component.data}
            canRemove={canRemoveComponent(component.type)}
            integrationId={integrationId}
          />
        );

      case 'TITLE':
        return (
          <ZNSTitleComponent
            onAdd={handleUpdate}
            onRemove={handleRemove}
            data={component.data}
            canRemove={canRemoveComponent(component.type)}
          />
        );

      case 'PARAGRAPH':
        return (
          <ZNSParagraphComponent
            onAdd={handleUpdate}
            onRemove={handleRemove}
            data={component.data}
            canRemove={canRemoveComponent(component.type)}
          />
        );

      case 'TABLE':
        return (
          <ZNSTableComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      case 'BUTTONS':
        return (
          <ZNSButtonsComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      case 'VOUCHER':
        return (
          <ZNSVoucherComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      case 'PAYMENT':
        return (
          <ZNSPaymentComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      case 'OTP':
        return (
          <ZNSOTPComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      case 'RATING':
        return (
          <ZNSRatingComponent onAdd={handleUpdate} onRemove={handleRemove} data={component.data} />
        );

      default:
        return (
          <div className="border border-border rounded-lg p-3 bg-muted/20">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="caption" className="font-medium text-primary">
                {component.type}
              </Typography>
              <Button variant="ghost" size="sm" onClick={handleRemove}>
                <Icon name="x" size="sm" />
              </Button>
            </div>
            <Typography variant="caption" className="text-muted-foreground">
              Component chưa được implement
            </Typography>
          </div>
        );
    }
  };

  const { headerComponents, bodyComponents, footerComponents } = getComponentsBySection();

  // Render component tabs (button style)
  const renderComponentTabs = (components: ComponentType[]) => {
    if (components.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2">
        {components.map(component => {
          const limits = componentLimits[component.type];
          const currentCount = selectedComponents.filter(
            comp => comp.type === component.type
          ).length;
          const canAdd = canAddComponent(component.type);
          const isRequired = limits?.min === 1 && limits?.max === 1;
          const isSingle = limits?.max === 1;
          const isActive = currentCount > 0;

          // Don't show button for required components (they auto-show)
          if (isRequired) return null;

          return (
            <Button
              key={component.id}
              variant={isActive ? 'primary' : 'outline'}
              size="sm"
              disabled={!canAdd && currentCount === 0}
              onClick={() => {
                if (isSingle && currentCount === 0) {
                  handleAddComponent(component);
                } else if (!isSingle && canAdd) {
                  handleAddComponent(component);
                }
              }}
              className={`
                flex items-center gap-2 h-8 px-3 w-auto text-xs
                ${isActive ? 'bg-primary text-primary-foreground' : 'bg-background border-border'}
                ${!canAdd && currentCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <Icon name={component.icon as any} size="sm" />
              <span className="font-medium">{component.name}</span>
              {!isSingle && canAdd && <Icon name="plus" size="sm" />}
            </Button>
          );
        })}
      </div>
    );
  };

  // Render inline components for a section
  const renderInlineComponents = (components: ComponentType[]) => {
    const sectionComponents = selectedComponents.filter(comp =>
      components.some(c => c.type === comp.type)
    );

    if (sectionComponents.length === 0) return null;

    return (
      <div className="space-y-3">
        {sectionComponents.map(comp => (
          <div key={comp.id}>{renderInlineComponent(comp)}</div>
        ))}
      </div>
    );
  };

  // Render components with accordion wrapper grouped by sections
  const renderComponentsWithAccordion = () => {
    const sortedComponents = sortComponentsByOrder(selectedComponents, templateType || '');
    const templateConfig = getTemplateConfig(templateType || '');
    const availableRules = templateConfig?.rules || [];

    // Group rules by section
    const sections = ['header', 'body', 'footer'] as const;
    const sectionTitles = {
      header: t('marketing:zalo.zns.componentSelector.sections.header'),
      body: t('marketing:zalo.zns.componentSelector.sections.body'),
      footer: t('marketing:zalo.zns.componentSelector.sections.footer'),
    };

    return (
      <div className="space-y-6">
        {sections.map(section => {
          const sectionRules = availableRules.filter(rule => rule.section === section);
          if (sectionRules.length === 0) return null;

          const sortedSectionComponents = sortedComponents.filter(comp =>
            sectionRules.some(rule => rule.type === comp.type)
          );

          // Special handling for header section (Logo + Images) - Template-aware
          if (section === 'header') {
            const logoComponents = sortedComponents.filter(comp => comp.type === 'LOGO');
            const imageComponents = sortedComponents.filter(comp => comp.type === 'IMAGES');
            const hasLogo = logoComponents.length > 0;
            const hasImages = imageComponents.length > 0;

            // Check if template supports both LOGO and IMAGES
            const availableComponents = getAvailableComponents();
            const supportsLogo = availableComponents.some(comp => comp.type === 'LOGO');
            const supportsImages = availableComponents.some(comp => comp.type === 'IMAGES');

            // Check if LOGO is required for this template
            const componentLimits = getComponentLimits();
            const logoRequired = componentLimits['LOGO']?.min > 0;

            // If template doesn't support header components, don't render
            if (!supportsLogo && !supportsImages) {
              return null;
            }

            return (
              <div key={section} className=" rounded-lg p-4 space-y-3">
                {/* Section Title */}
                <div className="flex items-center justify-between">
                  <Typography variant="body1" className="font-medium">
                    {sectionTitles[section]}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {logoComponents.length + imageComponents.length}{' '}
                    {t('marketing:zalo.zns.componentSelector.sections.ingredients')}
                  </Typography>
                </div>

                {/* For templates with required LOGO, show component directly without buttons */}
                {logoRequired && supportsLogo && !supportsImages ? (
                  <div className="space-y-2">
                    {hasLogo ? (
                      logoComponents.map(comp => (
                        <div key={comp.id} className="bg-muted/30 rounded-lg p-3">
                          {renderInlineComponent(comp)}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <Typography variant="body2">Logo component sẽ được thêm tự động</Typography>
                      </div>
                    )}
                  </div>
                ) : (
                  <>
                    {/* Choice Buttons - Only show for templates that support both or optional components */}
                    {(supportsLogo || supportsImages) &&
                      ((supportsLogo && supportsImages) || !logoRequired) && (
                        <div className="flex flex-wrap gap-2">
                          {supportsLogo && (
                            <Button
                              variant={hasLogo ? 'primary' : 'outline'}
                              size="sm"
                              onClick={async () => {
                                // Always allow switching to Logo
                                // Step 1: Remove Images first (with animation)
                                if (hasImages) {
                                  const updatedComponents = selectedComponents.filter(
                                    comp => comp.type !== 'IMAGES'
                                  );
                                  setSelectedComponents(updatedComponents);
                                  onComponentsChangeRef.current(updatedComponents);

                                  // Step 2: Wait a bit, then add Logo (if not already present)
                                  setTimeout(() => {
                                    if (!hasLogo) {
                                      handleAddComponent('LOGO');
                                    }
                                  }, 150);
                                } else if (!hasLogo) {
                                  // No Images to remove, add Logo directly
                                  handleAddComponent('LOGO');
                                }
                              }}
                              className="h-8 text-xs px-3 w-auto"
                            >
                              <Icon name="image" size="xs" className="mr-1" />
                              Logo
                            </Button>
                          )}

                          {supportsImages && (
                            <Button
                              variant={hasImages ? 'primary' : 'outline'}
                              size="sm"
                              onClick={async () => {
                                // Always allow switching to Images
                                // Step 1: Remove Logo first (with animation)
                                if (hasLogo) {
                                  const updatedComponents = selectedComponents.filter(
                                    comp => comp.type !== 'LOGO'
                                  );
                                  setSelectedComponents(updatedComponents);
                                  onComponentsChangeRef.current(updatedComponents);

                                  // Step 2: Wait a bit, then add Images (if not already present)
                                  setTimeout(() => {
                                    if (!hasImages) {
                                      handleAddComponent('IMAGES');
                                    }
                                  }, 150);
                                } else if (!hasImages) {
                                  // No Logo to remove, add Images directly
                                  handleAddComponent('IMAGES');
                                }
                              }}
                              className="h-8 text-xs px-3 w-auto"
                            >
                              <Icon name="image" size="xs" className="mr-1" />
                              {t('marketing:zalo.zns.componentSelector.components.images.name')}
                            </Button>
                          )}
                        </div>
                      )}

                    {/* Component Display - Only show active component with animation */}
                    {hasLogo && (
                      <div className="space-y-2 animate-in fade-in duration-200">
                        {logoComponents.map(comp => (
                          <div key={comp.id} className="bg-muted/30 rounded-lg p-3">
                            {renderInlineComponent(comp)}
                          </div>
                        ))}
                      </div>
                    )}

                    {hasImages && (
                      <div className="space-y-2 animate-in fade-in duration-200">
                        {imageComponents.map(comp => (
                          <div key={comp.id} className="bg-muted/30 rounded-lg p-3">
                            {renderInlineComponent(comp)}
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}
              </div>
            );
          }

          // Regular sections (body, footer) - Clean Design
          return (
            <div key={section} className=" rounded-lg p-4 space-y-3">
              {/* Section Header */}
              <div className="flex items-center justify-between">
                <Typography variant="body1" className="font-medium">
                  {sectionTitles[section]}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {sortedSectionComponents.length}{' '}
                  {t('marketing:zalo.zns.componentSelector.sections.ingredients')}
                </Typography>
              </div>

              {/* Add Component Buttons - Flexible Layout */}
              <div className="flex flex-wrap gap-2">
                {sectionRules.slice(0, 8).map(rule => {
                  const existingComponents = sortedComponents.filter(
                    comp => comp.type === rule.type
                  );
                  if (existingComponents.length >= rule.max) return null;

                  const isActive = existingComponents.length > 0;

                  return (
                    <Button
                      key={rule.type}
                      variant={isActive ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => handleAddComponent(rule.type)}
                      className="h-8 text-xs px-3 w-auto"
                    >
                      <Icon name="plus" size="xs" className="mr-1" />
                      {getComponentDisplayName(rule.type)}
                      {rule.max > 1 && existingComponents.length > 0 && (
                        <span className="ml-1 opacity-70">({existingComponents.length})</span>
                      )}
                    </Button>
                  );
                })}
              </div>

              {/* Existing Components */}
              {sortedSectionComponents.length > 0 && (
                <div className="space-y-2">
                  {sortedSectionComponents.map(comp => (
                    <div key={comp.id} className="bg-muted/30 rounded-lg p-3">
                      {renderInlineComponent(comp)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const getComponentDisplayName = (type: string): string => {
    const names: Record<string, string> = {
      LOGO: t('marketing:zalo.zns.componentSelector.componentNames.logo'),
      IMAGES: t('marketing:zalo.zns.componentSelector.componentNames.images'),
      TITLE: t('marketing:zalo.zns.componentSelector.componentNames.title'),
      PARAGRAPH: t('marketing:zalo.zns.componentSelector.componentNames.paragraph'),
      TABLE: t('marketing:zalo.zns.componentSelector.componentNames.table'),
      OTP: t('marketing:zalo.zns.componentSelector.componentNames.otp'),
      VOUCHER: t('marketing:zalo.zns.componentSelector.componentNames.voucher'),
      PAYMENT: t('marketing:zalo.zns.componentSelector.componentNames.payment'),
      RATING: t('marketing:zalo.zns.componentSelector.componentNames.rating'),
      BUTTONS: t('marketing:zalo.zns.componentSelector.componentNames.buttons'),
    };
    return names[type] || type;
  };

  return (
    <div className="">
      <div className="space-y-4">
        {/* Content */}
        {templateType ? (
          renderComponentsWithAccordion()
        ) : (
          <div className="space-y-6">
            {/* Header Components */}
            {headerComponents.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Typography variant="subtitle1" className="font-semibold text-foreground">
                    {t('marketing:zalo.zns.componentSelector.sections.headerRequired')}
                  </Typography>
                </div>
                {renderComponentTabs(headerComponents)}
                {renderInlineComponents(headerComponents)}
              </div>
            )}

            {/* Body Components */}
            {bodyComponents.length > 0 && (
              <div className="space-y-4">
                <Typography variant="subtitle1" className="font-semibold text-foreground">
                  Nội dung ZNS
                </Typography>
                {renderComponentTabs(bodyComponents)}
                {renderInlineComponents(bodyComponents)}
              </div>
            )}

            {/* Footer Components */}
            {footerComponents.length > 0 && (
              <div className="space-y-4">
                <Typography variant="subtitle1" className="font-semibold text-foreground">
                  Nút thao tác
                </Typography>
                {renderComponentTabs(footerComponents)}
                {renderInlineComponents(footerComponents)}
              </div>
            )}

            {/* Validation Errors */}
            {!validationState.isValid && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-2">
                <div className="flex items-start gap-2">
                  <Icon
                    name="alert-circle"
                    className="text-destructive flex-shrink-0 mt-0.5"
                    size="sm"
                  />
                  <div className="flex-1">
                    <Typography variant="caption" className="text-destructive font-medium mb-1">
                      Lỗi cấu hình
                    </Typography>
                    <div className="space-y-1">
                      {validationState.errors.map((error, index) => (
                        <Typography
                          key={index}
                          variant="caption"
                          className="text-destructive/80 block text-xs"
                        >
                          • {error}
                        </Typography>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Validation Errors for Accordion Mode */}
        {templateType && !validationState.isValid && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Icon
                name="alert-circle"
                className="text-destructive flex-shrink-0 mt-0.5"
                size="sm"
              />
              <div className="flex-1">
                <Typography variant="body2" className="text-destructive font-medium mb-2">
                  Cần hoàn thiện template
                </Typography>
                <div className="space-y-1">
                  {validationState.errors.map((error, index) => (
                    <Typography key={index} variant="caption" className="text-destructive/80 block">
                      • {error}
                    </Typography>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(ZNSComponentSelector);
