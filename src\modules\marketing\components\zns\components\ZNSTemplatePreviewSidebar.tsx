import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { sortComponentsByOrder } from '../utils/znsComponentOrder';

// Image Slideshow Component for Preview
interface ImageSlideshowProps {
  images: Array<{ url: string; name: string }>;
  autoSlide?: boolean;
  interval?: number;
}

const ImageSlideshow: React.FC<ImageSlideshowProps> = ({
  images,
  autoSlide = true,
  interval = 4000,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!autoSlide || images.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [autoSlide, images.length, interval]);

  if (images.length === 0) return null;

  return (
    <div className="relative w-full h-32 bg-muted rounded overflow-hidden">
      <img
        src={images[currentIndex]?.url}
        alt={`Slide ${currentIndex + 1}`}
        className="w-full h-full object-cover transition-opacity duration-500"
      />

      {images.length > 1 && (
        <>
          {/* Dots indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
            {images.map((_, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>

          {/* Image counter */}
          <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            {currentIndex + 1}/{images.length}
          </div>
        </>
      )}
    </div>
  );
};

interface ZNSTemplatePreviewSidebarProps {
  templateType: string;
  components: any[];
  isOpen: boolean;
  isInline?: boolean;
}

const ZNSTemplatePreviewSidebar: React.FC<ZNSTemplatePreviewSidebarProps> = ({
  templateType,
  components,
  isOpen,
  isInline = false,
}) => {
  const { t } = useTranslation(['marketing']);
  const [previewKey, setPreviewKey] = useState(0);
  const [lastTemplateType, setLastTemplateType] = useState(templateType);
  const [lastComponentsHash, setLastComponentsHash] = useState('');

  // Reset preview completely when templateType changes
  useEffect(() => {
    if (templateType !== lastTemplateType) {
      console.log('🔄 [ZNSTemplatePreviewSidebar] Template type changed:', {
        from: lastTemplateType,
        to: templateType,
      });

      // Force complete reset with new key
      setPreviewKey(Date.now());
      setLastTemplateType(templateType);
      setLastComponentsHash('');
    }
  }, [templateType, lastTemplateType]);

  // Update preview when components change (but same template type)
  useEffect(() => {
    if (templateType === lastTemplateType && components) {
      const componentsHash = JSON.stringify(components);

      if (componentsHash !== lastComponentsHash) {
        console.log('🔄 [ZNSTemplatePreviewSidebar] Components updated:', {
          templateType,
          componentsCount: components?.length || 0,
        });

        // Gentle update for same template type
        setPreviewKey(prev => prev + 1);
        setLastComponentsHash(componentsHash);
      }
    }
  }, [components, templateType, lastTemplateType, lastComponentsHash]);

  // Component to render text with styled parameters
  const TextWithParameters: React.FC<{ text: string }> = ({ text }) => {
    if (!text) return <>{text}</>;

    const parts = text.split(/(<[^>]+>)/g);
    return (
      <>
        {parts.map((part, index) => {
          if (part.match(/^<[^>]+>$/)) {
            // This is a parameter - render in italic with theme-aware colors
            return (
              <span key={index} className="italic font-medium text-xs text-primary">
                {part}
              </span>
            );
          }
          return <span key={index}>{part}</span>;
        })}
      </>
    );
  };

  // Render ZNS Preview with real data from components
  const renderZNSPreview = () => {
    if (!templateType) {
      return (
        <div className="text-center py-6">
          <Typography variant="body2" className="text-muted-foreground text-center">
            Chọn loại template để xem preview
          </Typography>
        </div>
      );
    }

    if (!components || components.length === 0) {
      return (
        <div className="text-center py-6">
          <Typography variant="body2" className="text-muted-foreground text-center">
            {t('marketing:zalo.zns.preview.noContent')}
          </Typography>
        </div>
      );
    }

    const sortedComponents = sortComponentsByOrder(components, templateType);

    return (
      <div
        key={`template-${templateType}-${previewKey}`}
        className="space-y-1 bg-white dark:bg-gray-800"
      >
        {/* Render all components in correct order - Zalo OA Style */}
        {sortedComponents.map((component, index) => (
          <div key={`${templateType}-component-${component.type}-${index}-${previewKey}`}>
            {component.type === 'LOGO' && (
              <div className="flex justify-center mb-4">
                {(() => {
                  // Use light logo as default, fallback to dark logo if needed
                  const logoUrl =
                    component.data?.LOGO?.light?.url ||
                    component.data?.logo?.light?.url ||
                    component.data?.LOGO?.dark?.url ||
                    component.data?.logo?.dark?.url ||
                    component.data?.logo?.url ||
                    component.data?.url;

                  return logoUrl ? (
                    <div className="relative bg-white dark:bg-gray-800 p-2 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                      <img
                        src={logoUrl}
                        alt={t('marketing:zalo.zns.components.logo.label')}
                        className="h-8 w-auto object-contain"
                      />
                    </div>
                  ) : (
                    <div className="h-8 px-4 rounded-lg flex items-center justify-center text-sm bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
                      Logo
                    </div>
                  );
                })()}
              </div>
            )}

            {component.type === 'IMAGES' && (
              <div className="">
                {component.data?.images && component.data.images.length > 0 ? (
                  <div className="rounded-lg overflow-hidden shadow-sm">
                    <ImageSlideshow
                      images={component.data.images.map((img: any) => ({
                        url: img.url,
                        name: img.name || 'Image',
                      }))}
                    />
                  </div>
                ) : component.data?.image ? (
                  <div className="relative rounded-lg overflow-hidden shadow-sm">
                    <img
                      src={component.data.image.url}
                      alt={t('marketing:zalo.zns.preview.imageAlt')}
                      className="w-full h-40 object-cover"
                    />
                  </div>
                ) : (
                  <div className="rounded-lg h-32 flex items-center justify-center text-sm bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600">
                    <div className="text-center">
                      <svg
                        className="w-8 h-8 mx-auto mb-2 text-gray-400 dark:text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      Hình ảnh
                    </div>
                  </div>
                )}
              </div>
            )}

            {component.type === 'TITLE' && (
              <div className="mb-1 px-2">
                <Typography
                  variant="body1"
                  className="font-medium text-sm text-gray-900 dark:text-gray-100"
                >
                  <TextWithParameters text={component.data?.title || 'Tiêu đề tin nhắn'} />
                </Typography>
              </div>
            )}

            {component.type === 'PARAGRAPH' && (
              <div className="mb-3 px-2">
                <Typography
                  variant="body2"
                  className="leading-relaxed text-gray-700 dark:text-gray-300"
                >
                  <TextWithParameters text={component.data?.content || 'Đoạn văn Zalo'} />
                </Typography>
              </div>
            )}

            {component.type === 'TABLE' && (
              <div className="mb-1 px-2">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
                  {component.data?.rows?.length > 0 ? (
                    component.data.rows.map((row: any, rowIndex: number) => (
                      <div
                        key={rowIndex}
                        className="flex border-b last:border-b-0 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex-1 px-2 py-1 text-xs font-normal text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-r border-gray-200 dark:border-gray-600">
                          <TextWithParameters text={row.title || ''} />
                        </div>
                        <div className="flex-1 px-2 py-1 text-gray-900 dark:text-gray-100">
                          <TextWithParameters text={row.value || ''} />
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-6 text-center">
                      <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                        Bảng thông tin
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            )}

            {component.type === 'OTP' && (
              <div className="mb-4">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                  <div className="text-center">
                    <Typography
                      variant="body2"
                      className="font-medium mb-4 text-gray-700 dark:text-gray-300"
                    >
                      Mã xác thực OTP của bạn
                    </Typography>

                    <div className="inline-flex items-center justify-center bg-white dark:bg-gray-800 rounded-lg px-6 py-4 shadow-sm border-2 border-blue-200 dark:border-blue-600 mb-4">
                      <span className="font-mono text-2xl font-bold text-blue-600 dark:text-blue-400 tracking-widest">
                        {component.data?.otp || '123456'}
                      </span>
                    </div>

                    <Typography
                      variant="caption"
                      className="text-gray-600 dark:text-gray-400 leading-relaxed block"
                    >
                      {component.data?.value ||
                        'Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã có hiệu lực trong 5 phút.'}
                    </Typography>
                  </div>
                </div>
              </div>
            )}

            {component.type === 'VOUCHER' && (
              <div className="mb-4">
                {(() => {
                  console.log(
                    '🎫 [ZNSTemplatePreviewSidebar] VOUCHER component data:',
                    component.data
                  );
                  return null;
                })()}
                <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/30 dark:to-red-900/30 rounded-lg p-6 border-2 border-dashed border-orange-300 dark:border-orange-600 relative overflow-hidden">
                  {/* Decorative circles */}
                  <div className="absolute -left-3 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-white dark:bg-gray-800 rounded-full border-2 border-orange-300 dark:border-orange-600"></div>
                  <div className="absolute -right-3 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-white dark:bg-gray-800 rounded-full border-2 border-orange-300 dark:border-orange-600"></div>

                  <div className="text-center">
                    <div className="flex items-center justify-center mb-3">
                      <svg
                        className="w-6 h-6 text-orange-500 dark:text-orange-400 mr-2"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                      </svg>
                      <Typography
                        variant="body2"
                        className="font-bold text-orange-700 dark:text-orange-300"
                      >
                        {t('marketing:zalo.zns.preview.voucher.title')}
                      </Typography>
                    </div>

                    <Typography
                      variant="h4"
                      className="font-bold text-orange-600 dark:text-orange-400 mb-2"
                    >
                      {component.data?.name ||
                        component.data?.voucher?.name ||
                        t('marketing:zalo.zns.preview.voucher.defaultName')}
                    </Typography>

                    <Typography variant="body2" className="text-gray-700 dark:text-gray-300 mb-2">
                      {component.data?.condition ||
                        component.data?.voucher?.condition ||
                        t('marketing:zalo.zns.preview.voucher.defaultCondition')}
                    </Typography>

                    {/* Voucher Code Display */}
                    {(component.data?.voucher_code || component.data?.voucher?.voucher_code) && (
                      <div className="mt-3 p-2 bg-white dark:bg-gray-800 rounded border border-orange-200 dark:border-orange-700">
                        <Typography
                          variant="caption"
                          className="text-gray-500 dark:text-gray-400 block mb-1"
                        >
                          {t('marketing:zalo.zns.preview.voucher.code')}
                        </Typography>
                        <Typography
                          variant="body2"
                          className="font-mono font-bold text-orange-600 dark:text-orange-400"
                        >
                          {component.data?.voucher_code || component.data?.voucher?.voucher_code}
                        </Typography>
                      </div>
                    )}

                    {/* Date Range */}
                    {(component.data?.start_date ||
                      component.data?.voucher?.start_date ||
                      component.data?.end_date ||
                      component.data?.voucher?.end_date) && (
                      <div className="mt-2">
                        <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                          {(component.data?.start_date || component.data?.voucher?.start_date) &&
                          (component.data?.end_date || component.data?.voucher?.end_date)
                            ? `${component.data?.start_date || component.data?.voucher?.start_date} - ${component.data?.end_date || component.data?.voucher?.end_date}`
                            : component.data?.end_date || component.data?.voucher?.end_date
                              ? `${t('marketing:zalo.zns.preview.voucher.validUntil')} ${component.data?.end_date || component.data?.voucher?.end_date}`
                              : `${t('marketing:zalo.zns.preview.voucher.validFrom')} ${component.data?.start_date || component.data?.voucher?.start_date}`}
                        </Typography>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {component.type === 'RATING' && (
              <div className="mb-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                  <div className="text-center">
                    <Typography
                      variant="body2"
                      className="font-medium mb-4 text-gray-700 dark:text-gray-300"
                    >
                      {t('marketing:zalo.zns.preview.rating.title')}
                    </Typography>

                    <div className="flex justify-center gap-2 mb-3">
                      {[1, 2, 3, 4, 5].map(star => (
                        <svg
                          key={star}
                          className="w-8 h-8 text-yellow-400 dark:text-yellow-300 hover:text-yellow-500 dark:hover:text-yellow-200 cursor-pointer transition-colors"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                        </svg>
                      ))}
                    </div>

                    <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
                      {t('marketing:zalo.zns.preview.rating.subtitle')}
                    </Typography>
                  </div>
                </div>
              </div>
            )}

            {component.type === 'BUTTONS' && (
              <div className="mb-4 mt-2 mx-2 space-y-3">
                {(component.data?.items || component.data?.buttons)?.map(
                  (button: any, buttonIndex: number) => (
                    <div key={buttonIndex} className="w-full">
                      <button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white text-center py-3 px-6 rounded-lg font-medium transition-colors shadow-sm border border-blue-700 dark:border-blue-600">
                        {button.title || t('marketing:zalo.zns.preview.buttons.defaultText')}
                      </button>
                    </div>
                  )
                ) || (
                  <button className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white text-center py-3 px-6 rounded-lg font-medium transition-colors shadow-sm border border-blue-700 dark:border-blue-600">
                    {t('marketing:zalo.zns.preview.buttons.defaultText')}
                  </button>
                )}
              </div>
            )}

            {component.type === 'PAYMENT' && (
              <div className="mb-4">
                {/* Payment Card - Modern Design */}
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 rounded-lg border border-green-200 dark:border-green-700 overflow-hidden">
                  {/* Payment Header */}

                  {/* Bank Info */}
                  <div className="p-4 bg-white dark:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-8 bg-blue-600 dark:bg-blue-700 rounded flex items-center justify-center">
                        <span className="text-white font-bold text-xs">BANK</span>
                      </div>
                      <div>
                        <Typography
                          variant="body2"
                          className="font-bold text-gray-900 dark:text-gray-100"
                        >
                          {component.data?.payment?.bankName || 'Ngân hàng Eximbank'}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                          Chi nhánh Hà Nội
                        </Typography>
                      </div>
                    </div>
                  </div>

                  {/* Payment Details */}
                  <div className="p-4 bg-white dark:bg-gray-800 space-y-3">
                    <div className="grid grid-cols-1 gap-3">
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400 text-xs font-medium">
                          Tên tài khoản
                        </span>
                        <span className="font-bold text-xs text-gray-900 dark:text-gray-100 text-right">
                          {component.data?.PAYMENT?.account_name || 'CÔNG TY TNHH ABC'}
                        </span>
                      </div>

                      <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400 text-xs font-medium">
                          Số tài khoản
                        </span>
                        <span className="font-mono font-bold text-blue-600 text-xs dark:text-blue-400 text-right">
                          {component.data?.PAYMENT?.bank_account || '**********'}
                        </span>
                      </div>

                      <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400 text-xs font-medium">
                          Số tiền
                        </span>
                        <span className="font-bold text-green-600 dark:text-green-400 text-xs text-right">
                          {component.data?.PAYMENT?.amount?.includes('<') &&
                          component.data?.PAYMENT?.amount?.includes('>') ? (
                            <span className="italic text-gray-500 dark:text-gray-400">
                              {component.data?.PAYMENT?.amount}
                            </span>
                          ) : (
                            `${component.data?.PAYMENT?.amount || '1,000,000'} VND`
                          )}
                        </span>
                      </div>

                      <div className="flex justify-between items-start py-2">
                        <span className="text-gray-600 dark:text-gray-400 text-xs font-medium">
                          Nội dung CK
                        </span>
                        <span className="font-medium text-gray-900 dark:text-gray-100 text-xs text-right max-w-[60%]">
                          {component.data?.PAYMENT?.note?.includes('<') &&
                          component.data?.PAYMENT?.note?.includes('>') ? (
                            <span className="italic text-gray-500 dark:text-gray-400">
                              {component.data?.PAYMENT?.note}
                            </span>
                          ) : (
                            component.data?.PAYMENT?.note || 'Thanh toan don hang'
                          )}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* QR Code Section */}
                  <div className="p-4 bg-gray-50 border-t border-green-100">
                    <div className="text-center">
                      <div className="inline-block bg-white p-4 rounded-lg shadow-sm border-2 border-dashed border-gray-300">
                        <div className="w-24 h-24 bg-gray-100 rounded flex items-center justify-center mb-2">
                          <svg
                            className="w-12 h-12 text-gray-400"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 13h2v2h-2zM19 15h2v2h-2z" />
                          </svg>
                        </div>
                        <Typography variant="caption" className="text-gray-600 font-medium">
                          Quét mã QR để thanh toán
                        </Typography>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  if (!isOpen) return null;

  // Inline mode - Modern iPhone-style Design like VietGuys
  if (isInline) {
    return (
      <div className="h-full flex flex-col">
        {/* Content */}
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="w-full">
            <div className="relative">
              {/* Phone Shadow */}
              <div className="absolute inset-0 bg-black/30 rounded-[3.5rem] blur-3xl transform translate-y-8 scale-95"></div>

              {/* Phone Body - Realistic iPhone Design */}
              <div className="relative w-80 h-[580px] mx-auto">
                {/* Outer Frame - Black Metal */}
                <div className="w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-[3.5rem] p-1 shadow-2xl">
                  {/* Inner Frame - Screen Bezel */}
                  <div className="w-full h-full bg-black rounded-[3.2rem] p-1">
                    {/* Screen */}
                    <div className="w-full h-full rounded-[3rem] overflow-hidden relative bg-white">
                      {/* Status Bar */}
                      <div className="px-6 pt-4 pb-3 flex justify-between items-center text-sm font-semibold bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                        <div className="flex items-center gap-2">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
                          </svg>
                          <Typography variant="body2" className="text-white font-semibold">
                            Red AI
                          </Typography>
                        </div>

                        <div className="flex items-center gap-1">
                          <Typography variant="caption" className="text-white font-bold">
                            9:41
                          </Typography>
                          <div className="w-6 h-3 border border-white rounded-sm ml-2">
                            <div className="w-4 h-1 rounded-sm m-0.5 bg-white"></div>
                          </div>
                        </div>
                      </div>

                      {/* Chat Content */}
                      <div className="h-[450px] p-4 relative overflow-y-auto dark:bg-gray-800 bg-gray-100">
                        {/* ZNS Message Card - Clean Content */}
                        <div className="mb-4">
                          <div className="rounded-2xl shadow-lg  overflow-hidden bg-white dark:bg-gray-800 max-w-[95%] border-gray-200 ">
                            {/* Clean ZNS Template Content - No Header */}
                            <div className=" space-y-3">{renderZNSPreview()}</div>
                          </div>
                        </div>
                      </div>

                      {/* Bottom Navigation Bar */}
                      <div className="absolute bottom-0 left-0 right-0 bg-white  dark:bg-gray-800 border-t dark:border-gray-700 border-gray-200 px-6 py-4">
                        <div className="flex justify-between items-center">
                          <div className="flex flex-col items-center gap-1">
                            <div className="w-6 h-6 bg-gray-300 rounded"></div>
                            <Typography variant="caption" className=" text-xs">
                              Chi tiết
                            </Typography>
                          </div>
                          <div className="flex flex-col items-center gap-1">
                            <div className="w-6 h-6 bg-gray-300 rounded"></div>
                            <Typography variant="caption" className=" text-xs">
                              Đánh giá DV
                            </Typography>
                          </div>
                          <div className="flex flex-col items-center gap-1">
                            <div className="w-6 h-6 bg-gray-300 rounded"></div>
                            <Typography variant="caption" className=" text-xs">
                              Mua ngay
                            </Typography>
                          </div>
                        </div>
                      </div>

                      {/* Home Indicator */}
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Sidebar mode - simplified layout
  return (
    <>
      {/* Global CSS for hiding scrollbars */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
        `,
        }}
      />

      <div className="h-full border lg:border-l w-full lg:w-80 flex flex-col bg-background dark:bg-gray-900 border-border dark:border-gray-700 rounded-lg lg:rounded-none">
        {/* Header */}
        <div className="p-3 lg:p-3 border-b flex-shrink-0 flex items-center justify-between bg-muted/50 dark:bg-gray-800/50 border-border dark:border-gray-700">
          <Typography
            variant="body1"
            className="font-medium text-foreground dark:text-gray-100 text-sm lg:text-base"
          >
            {t('marketing:zalo.zns.preview.title')}
          </Typography>
        </div>

        {/* Content */}
        <div
          className="flex-1 overflow-y-auto p-3 lg:p-4 bg-background dark:bg-gray-900 scrollbar-hide min-h-[300px] lg:min-h-0"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          <div key={previewKey}>{renderZNSPreview()}</div>
        </div>
      </div>
    </>
  );
};

export default React.memo(ZNSTemplatePreviewSidebar);
