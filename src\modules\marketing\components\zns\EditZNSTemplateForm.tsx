/* eslint-disable no-case-declarations */
import React, { useState, useCallback } from 'react';

import {
  Typography,
  Input,
  Select,
  Card,
  Icon,
  ResponsiveGrid,
  Stepper,
  IconCard,
  Textarea,
  Modal,
  Button,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import ZNSComponentSelector from './components/ZNSComponentSelector';
import ZNSTemplatePreviewSidebar from './components/ZNSTemplatePreviewSidebar';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: ZNSComponent[];
  note?: string;
  selectedOAId?: string;
}

interface ZNSComponent {
  id: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  data: any;
}

interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

const templateTypes = [
  { id: '1', name: 'ZNS thông báo giao dịch', icon: 'credit-card' },
  { id: '2', name: 'ZNS thông báo đơn hàng', icon: 'shopping-cart' },
  { id: '3', name: 'ZNS thông báo thanh toán', icon: 'dollar-sign' },
  { id: '4', name: 'ZNS thông báo vận chuyển', icon: 'truck' },
  { id: '5', name: 'ZNS đánh giá dịch vụ', icon: 'star' },
  { id: '6', name: 'ZNS khuyến mãi', icon: 'gift' },
  { id: '7', name: 'ZNS chăm sóc khách hàng', icon: 'headphones' },
  { id: '8', name: 'ZNS xác thực OTP', icon: 'shield' },
];

const steps = [
  { id: '1', title: 'Thông tin cơ bản', description: 'Tên và loại template' },
  { id: '2', title: 'Thiết kế nội dung', description: 'Thêm components' },
  { id: '3', title: 'Xem lại', description: 'Kiểm tra và lưu' },
];

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onClose,
  onSuccess,
}) => {
  const { success: showSuccess, error: showError } = useSmartNotification();
  const editMutation = useEditZNSTemplate();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [extractedParameters] = useState<ParameterData[]>([]);
  const [componentValidation, setComponentValidation] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });
  const [showValidation, setShowValidation] = useState(false);

  // Form data state - khởi tạo từ template hiện tại
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: template.templateName || '',
    tag: template.templateTag || '',
    templateType: template.templateType || '',
    components: [], // Sẽ được parse từ templateContent
    note: template.reason || '',
    selectedOAId: integrationId,
  });

  // Template type change modal
  const [showTemplateChangeModal, setShowTemplateChangeModal] = useState(false);
  const [pendingTemplateType, setPendingTemplateType] = useState<string | null>(null);

  // Update form data helper
  const updateFormData = useCallback((updater: (prev: TemplateFormData) => TemplateFormData) => {
    setFormData(updater);
  }, []);

  // Handle template type selection
  const handleTemplateTypeSelect = useCallback(
    (typeId: string) => {
      if (
        formData.templateType &&
        formData.templateType !== typeId &&
        formData.components.length > 0
      ) {
        setPendingTemplateType(typeId);
        setShowTemplateChangeModal(true);
      } else {
        updateFormData(prev => ({
          ...prev,
          templateType: typeId,
          components: [],
        }));
      }
    },
    [formData.templateType, formData.components.length, updateFormData]
  );

  // Confirm template type change
  const confirmTemplateTypeChange = useCallback(() => {
    if (pendingTemplateType) {
      updateFormData(prev => ({
        ...prev,
        templateType: pendingTemplateType,
        components: [],
      }));
    }
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, [pendingTemplateType, updateFormData]);

  // Handle components change
  const handleComponentsChange = useCallback(
    (components: ZNSComponent[]) => {
      updateFormData(prev => ({ ...prev, components }));
    },
    [updateFormData]
  );

  // Handle validation change
  const handleValidationChange = useCallback((isValid: boolean, errors: string[]) => {
    setComponentValidation(prev => {
      // Chỉ update khi thực sự có thay đổi
      if (prev.isValid === isValid && JSON.stringify(prev.errors) === JSON.stringify(errors)) {
        return prev;
      }
      return { isValid, errors };
    });

    // Reset validation display when validation changes
    if (isValid) {
      setShowValidation(false);
    }
  }, []);

  // Navigation functions
  const handleNext = useCallback(() => {
    if (currentStep === 2) {
      if (!componentValidation.isValid) {
        setShowValidation(true);
        return;
      }
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length));
  }, [currentStep, componentValidation.isValid]);

  const handlePrevious = useCallback(() => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  }, []);

  // Convert tag to number format
  const getTagNumber = (tag: string): string => {
    const tagMap: { [key: string]: string } = {
      TRANSACTION: '1',
      CUSTOMER_CARE: '2',
      PROMOTION: '3',
      OTP: '1', // OTP cũng là cấp độ 1
    };
    return tagMap[tag] || '1';
  };

  // Convert components to API format
  const convertComponentsToApiFormat = (components: ZNSComponent[]) => {
    return components.map(comp => {
      const componentData: any = {};

      switch (comp.type) {
        case 'TITLE':
          componentData[comp.type] = {
            value: comp.data?.title || comp.data?.content || '',
          };
          break;

        case 'PARAGRAPH':
          componentData[comp.type] = {
            value: comp.data?.content || '',
          };
          break;

        case 'BUTTONS':
          componentData[comp.type] = {
            items: (comp.data?.items || comp.data?.buttons || []).map((button: any) => ({
              type: button.type || 1,
              title: button.title || '',
              content: button.content || button.url || '',
            })),
          };
          break;

        case 'TABLE':
          componentData[comp.type] = {
            rows: comp.data?.rows || [],
          };
          break;

        case 'OTP':
          componentData[comp.type] = {
            value: comp.data?.content || comp.data?.value || '',
          };
          break;

        case 'IMAGES':
          componentData[comp.type] = {
            items: comp.data?.items || [],
          };
          break;

        case 'LOGO':
          componentData[comp.type] = {
            value: comp.data?.value || '',
          };
          break;

        case 'VOUCHER':
          componentData[comp.type] = comp.data || {};
          break;

        case 'PAYMENT':
          componentData[comp.type] = comp.data || {};
          break;

        case 'RATING':
          componentData[comp.type] = comp.data || {};
          break;

        default:
          componentData[comp.type] = comp.data || {};
      }

      return componentData;
    });
  };

  // Convert form data to API format
  const convertToApiFormat = (): any => {
    // Group components by section (header, body, footer)
    const headerComponents = formData.components.filter(comp => ['IMAGES'].includes(comp.type));
    const bodyComponents = formData.components.filter(comp =>
      ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(comp.type)
    );
    const footerComponents = formData.components.filter(comp => ['BUTTONS'].includes(comp.type));

    const layout: any = {};

    // Add header if has header components
    if (headerComponents.length > 0) {
      layout.header = {
        components: convertComponentsToApiFormat(headerComponents),
      };
    }

    // Always add body
    layout.body = {
      components: convertComponentsToApiFormat(bodyComponents),
    };

    // Add footer if has footer components
    if (footerComponents.length > 0) {
      layout.footer = {
        components: convertComponentsToApiFormat(footerComponents),
      };
    }

    const apiData = {
      template_name: formData.template_name,
      template_type: parseInt(formData.templateType),
      tag: getTagNumber(formData.tag),
      layout: layout,
      params: extractedParameters.map(param => ({
        name: param.name,
        type: 1, // Default type
        sample_value: param.sample_value,
      })),
      tracking_id: `template_${Date.now()}`,
      note: formData.note || '',
    };

    return apiData;
  };

  // Submit form
  const handleSubmit = useCallback(async () => {
    try {
      // Validate required fields
      if (!formData.template_name.trim()) {
        showError({ message: 'Tên template là bắt buộc' });
        return;
      }

      if (!formData.templateType) {
        showError({ message: 'Loại template là bắt buộc' });
        return;
      }

      if (formData.components.length === 0) {
        showError({ message: 'Template phải có ít nhất 1 component' });
        return;
      }

      // Convert to API format
      const apiData = convertToApiFormat();

      await editMutation.mutateAsync({
        integrationId,
        templateId: template.id.toString(),
        data: apiData,
      });

      showSuccess({ message: 'Cập nhật ZNS template thành công!' });
      onSuccess?.();
    } catch (error) {
      console.error('Error updating template:', error);
      showError({ message: 'Có lỗi xảy ra khi cập nhật template' });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    formData,
    extractedParameters,
    editMutation,
    template.id,
    integrationId,
    showSuccess,
    showError,
    onSuccess,
  ]);

  // Step 1: Basic Info
  const renderBasicInfoStep = () => (
    <div className="space-y-6 p-4">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Tên template <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.template_name}
            onChange={e => updateFormData(prev => ({ ...prev, template_name: e.target.value }))}
            placeholder="Nhập tên template"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Chọn loại nội dung ZNS <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.tag}
            onChange={value => updateFormData(prev => ({ ...prev, tag: value as string }))}
            options={[
              { value: 'TRANSACTION', label: 'Giao dịch (Cấp độ 1)' },
              { value: 'CUSTOMER_CARE', label: 'Chăm sóc khách hàng (Cấp độ 2)' },
              { value: 'PROMOTION', label: 'Khuyến mãi (Cấp độ 3)' },
              { value: 'OTP', label: 'OTP (Cấp độ 1)' },
            ]}
            placeholder="Chọn loại nội dung ZNS"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Ghi chú</label>
          <Textarea
            value={formData.note || ''}
            onChange={e => updateFormData(prev => ({ ...prev, note: e.target.value }))}
            placeholder="Nhập ghi chú cho template (tùy chọn)"
            rows={3}
          />
        </div>
      </div>
    </div>
  );

  // Step 2: Components
  const renderComponentsStep = () => (
    <div className="space-y-4 lg:space-y-6 p-4">
      <div className="mb-6">
        <Typography variant="h6" className="mb-4">
          Chọn loại template
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, lg: 3, xl: 4 }} gap={4}>
          {templateTypes.map(type => {
            const isSelected = formData.templateType === type.id;
            return (
              <Card
                key={type.id}
                className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                  isSelected
                    ? 'border-2 border-primary bg-primary/5 shadow-md'
                    : 'border border-gray-200 hover:border-primary/50'
                }`}
                onClick={() => handleTemplateTypeSelect(type.id)}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div
                    className={`p-3 rounded-full ${
                      isSelected ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    <Icon name={type.icon} size="lg" />
                  </div>
                  <Typography
                    variant="body2"
                    className={`font-medium ${isSelected ? 'text-primary' : 'text-gray-700'}`}
                  >
                    {type.name}
                  </Typography>
                </div>
              </Card>
            );
          })}
        </ResponsiveGrid>
      </div>

      {/* Component Selector */}
      {formData.templateType && (
        <div className="flex flex-col xl:flex-row gap-6">
          <div className="flex-1">
            <Card className="p-4">
              <Typography variant="h6" className="mb-4">
                Thiết kế nội dung
              </Typography>
              <div className="h-[500px] overflow-y-auto border rounded-lg p-2">
                <ZNSComponentSelector
                  key={`component-selector-${formData.templateType}`}
                  onComponentsChange={handleComponentsChange}
                  initialComponents={formData.components}
                  templateType={formData.templateType}
                  integrationId={formData.selectedOAId || integrationId}
                  onValidationChange={handleValidationChange}
                />
              </div>

              {/* Validation Errors */}
              {showValidation && !componentValidation.isValid && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <Typography variant="body2" className="text-red-600 font-medium mb-2">
                    Template chưa hoàn chỉnh
                  </Typography>
                  <ul className="text-sm text-red-500 space-y-1">
                    {componentValidation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </Card>
          </div>

          <div className="xl:w-80">
            <ZNSTemplatePreviewSidebar
              components={formData.components}
              templateType={formData.templateType}
              isOpen={true}
            />
          </div>
        </div>
      )}
    </div>
  );

  // Step 3: Review
  const renderReviewStep = () => (
    <div className="space-y-6 p-4">
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Xem lại thông tin
        </Typography>
        <div className="space-y-3">
          <div>
            <Typography variant="body2" className="font-medium">
              Tên template:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.template_name}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Loại nội dung ZNS:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.tag === 'TRANSACTION' && 'Giao dịch (Cấp độ 1)'}
              {formData.tag === 'CUSTOMER_CARE' && 'Chăm sóc khách hàng (Cấp độ 2)'}
              {formData.tag === 'PROMOTION' && 'Khuyến mãi (Cấp độ 3)'}
              {formData.tag === 'OTP' && 'OTP (Cấp độ 1)'}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Loại template:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {templateTypes.find(t => t.id === formData.templateType)?.name}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Số lượng components:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.components.length}
            </Typography>
          </div>
          {formData.note && (
            <div>
              <Typography variant="body2" className="font-medium">
                Ghi chú:
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {formData.note}
              </Typography>
            </div>
          )}
        </div>
      </Card>

      {/* Preview */}
      <div className="flex flex-col xl:flex-row gap-6">
        <div className="flex-1">
          <Card className="p-4">
            <Typography variant="h6" className="mb-4">
              Xem trước template
            </Typography>
            <div className="h-[400px] overflow-y-auto border rounded-lg p-2">
              <ZNSComponentSelector
                onComponentsChange={() => {}}
                initialComponents={formData.components}
                templateType={formData.templateType}
                integrationId={formData.selectedOAId || integrationId}
                onValidationChange={() => {}}
              />
            </div>
          </Card>
        </div>

        <div className="xl:w-80">
          <ZNSTemplatePreviewSidebar
            components={formData.components}
            templateType={formData.templateType}
            isOpen={true}
          />
        </div>
      </div>
    </div>
  );

  return (
    <ZNSAccordionProvider>
      <Card className="h-full">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <Typography variant="h5" className="mb-1">
              Chỉnh sửa ZNS Template
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              Template: {template.templateName}
            </Typography>
          </div>
          <IconCard icon="x" variant="ghost" size="md" title="Đóng" onClick={onClose} />
        </div>

        {/* Stepper */}
        <div className="px-6 mt-4">
          <Stepper
            steps={steps}
            currentStep={currentStep - 1}
            variant="filled"
            size="lg"
            showStepIcons
            colorScheme="primary"
          />
        </div>

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto">
          {currentStep === 1 && renderBasicInfoStep()}
          {currentStep === 2 && renderComponentsStep()}
          {currentStep === 3 && renderReviewStep()}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <Button
            variant="outline"
            onClick={currentStep === 1 ? onClose : handlePrevious}
            disabled={editMutation.isPending}
          >
            {currentStep === 1 ? 'Hủy' : 'Quay lại'}
          </Button>

          <div className="flex gap-2">
            {currentStep < steps.length ? (
              <Button onClick={handleNext} disabled={editMutation.isPending}>
                Tiếp theo
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                isLoading={editMutation.isPending}
                disabled={!componentValidation.isValid}
              >
                Cập nhật Template
              </Button>
            )}
          </div>
        </div>

        {/* Template Change Confirmation Modal */}
        <Modal
          isOpen={showTemplateChangeModal}
          onClose={() => setShowTemplateChangeModal(false)}
          title="Xác nhận thay đổi loại template"
        >
          <div className="space-y-4">
            <Typography variant="body1">
              Thay đổi loại template sẽ xóa tất cả components hiện tại. Bạn có chắc chắn muốn tiếp
              tục?
            </Typography>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowTemplateChangeModal(false)}>
                Hủy
              </Button>
              <Button onClick={confirmTemplateTypeChange}>Xác nhận</Button>
            </div>
          </div>
        </Modal>
      </Card>
    </ZNSAccordionProvider>
  );
};

export default EditZNSTemplateForm;
