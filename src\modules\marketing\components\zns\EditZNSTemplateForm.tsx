/* eslint-disable no-case-declarations */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Input,
  Select,
  Card,
  Icon,
  ResponsiveGrid,
  Stepper,
  IconCard,
  Textarea,
  Modal,
  Button,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import ZNSComponentSelector from './components/ZNSComponentSelector';
import ZNSTemplatePreviewSidebar from './components/ZNSTemplatePreviewSidebar';
import { ZNSTemplateService } from '../../services/zns-template.service';
import { ZaloService } from '../../services/zalo.service';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useMutation } from '@tanstack/react-query';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: ZNSComponent[];
  note?: string;
  selectedOAId?: string;
}

interface ZaloOfficialAccount {
  id: string;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  status: string;
}

interface ZNSComponent {
  id: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  data: any;
}

interface TemplateType {
  id: string;
  name: string;
  icon: string;
}

interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

const templateTypes: TemplateType[] = [
  {
    id: '1',
    name: 'marketing:zalo.zns.template.types.1',
    icon: 'settings',
  },
  {
    id: '2',
    name: 'marketing:zalo.zns.template.types.2',
    icon: 'shield',
  },
  {
    id: '3',
    name: 'marketing:zalo.zns.template.types.3',
    icon: 'credit-card',
  },
  {
    id: '4',
    name: 'marketing:zalo.zns.template.types.4',
    icon: 'credit-card',
  },
  {
    id: '5',
    name: 'marketing:zalo.zns.template.types.5',
    icon: 'star',
  },
  {
    id: '6',
    name: 'marketing:zalo.zns.template.types.6',
    icon: 'gift',
  },
  {
    id: '7',
    name: 'marketing:zalo.zns.template.types.7',
    icon: 'headphones',
  },
  {
    id: '8',
    name: 'marketing:zalo.zns.template.types.8',
    icon: 'shield',
  },
];

const steps = [
  {
    id: '1',
    title: 'marketing:zalo.zns.template.form.steps.basicInfo.title',
    description: 'marketing:zalo.zns.template.form.steps.basicInfo.description',
  },
  {
    id: '2',
    title: 'marketing:zalo.zns.template.form.steps.components.title',
    description: 'marketing:zalo.zns.template.form.steps.components.description',
  },
  {
    id: '3',
    title: 'marketing:zalo.zns.template.form.steps.review.title',
    description: 'marketing:zalo.zns.template.form.steps.review.description',
  },
];

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['marketing']);
  const { success: showSuccess, error: showError } = useSmartNotification();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [oaAccounts, setOaAccounts] = useState<ZaloOfficialAccount[]>([]);
  const [isLoadingOAs, setIsLoadingOAs] = useState(false);
  const [extractedParameters, setExtractedParameters] = useState<ParameterData[]>([]);
  const [parameterValues, setParameterValues] = useState<{
    [key: string]: { type: string; value: string };
  }>({});
  const [componentValidation, setComponentValidation] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });
  const [showValidation, setShowValidation] = useState(false);

  // Modal confirmation state
  const [showTemplateChangeModal, setShowTemplateChangeModal] = useState(false);
  const [pendingTemplateType, setPendingTemplateType] = useState<string | null>(null);

  // Form data - Initialize from existing template
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: template.templateName || '',
    tag: template.templateTag || '',
    templateType: template.templateType || '',
    components: [], // Will be parsed from template content
    note: template.reason || '',
    selectedOAId: integrationId,
  });

  // Edit mutation
  const editMutation = useEditZNSTemplate();

  // Load OA accounts on mount
  useEffect(() => {
    const loadOAAccounts = async () => {
      setIsLoadingOAs(true);
      try {
        const response = await ZaloService.getOfficialAccounts();
        if (response.code === 200 && response.result?.data) {
          setOaAccounts(response.result.data);
        }
      } catch (error) {
        console.error('Error loading OA accounts:', error);
      } finally {
        setIsLoadingOAs(false);
      }
    };

    loadOAAccounts();
  }, []);

  // Update form data helper
  const updateFormData = useCallback((updater: (prev: TemplateFormData) => TemplateFormData) => {
    setFormData(updater);
  }, []);

  // Handle template type selection
  const handleTemplateTypeSelect = useCallback(
    (typeId: string) => {
      if (
        formData.templateType &&
        formData.templateType !== typeId &&
        formData.components.length > 0
      ) {
        setPendingTemplateType(typeId);
        setShowTemplateChangeModal(true);
      } else {
        updateFormData(prev => ({
          ...prev,
          templateType: typeId,
          components: [],
        }));
      }
    },
    [formData.templateType, formData.components.length, updateFormData]
  );

  // Confirm template type change
  const confirmTemplateTypeChange = useCallback(() => {
    if (pendingTemplateType) {
      updateFormData(prev => ({
        ...prev,
        templateType: pendingTemplateType,
        components: [],
      }));
    }
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, [pendingTemplateType, updateFormData]);

  // Cancel template type change
  const cancelTemplateTypeChange = useCallback(() => {
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, []);

  // Handle components change
  const handleComponentsChange = useCallback(
    (components: ZNSComponent[]) => {
      updateFormData(prev => ({ ...prev, components }));
    },
    [updateFormData]
  );

  // Handle validation change from component selector
  const handleValidationChange = useCallback((isValid: boolean, errors: string[]) => {
    setComponentValidation(prev => {
      if (prev.isValid === isValid && JSON.stringify(prev.errors) === JSON.stringify(errors)) {
        return prev;
      }
      return { isValid, errors };
    });

    if (isValid) {
      setShowValidation(false);
    }
  }, []);

  // Navigation functions
  const handleNext = useCallback(() => {
    if (currentStep === 2) {
      if (!componentValidation.isValid) {
        setShowValidation(true);
        return;
      }
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length));
  }, [currentStep, componentValidation.isValid]);

  const handlePrevious = useCallback(() => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  }, []);

  // Validation functions
  const canProceedFromStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return (
          formData.selectedOAId !== '' &&
          formData.template_name.trim() !== '' &&
          formData.tag !== ''
        );
      case 2:
        return componentValidation.isValid && formData.components.length > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  // Convert tag to number format
  const getTagNumber = (tag: string): string => {
    const tagMap: { [key: string]: string } = {
      TRANSACTION: '1',
      CUSTOMER_CARE: '2',
      PROMOTION: '3',
      OTP: '1',
    };
    return tagMap[tag] || '1';
  };

  // Get parameter type number
  const getParameterTypeNumber = (type: string): number => {
    const typeMap: { [key: string]: number } = {
      text: 1,
      number: 2,
      date: 3,
    };
    return typeMap[type] || 1;
  };

  // Convert components to API format
  const convertComponentsToApiFormat = (components: ZNSComponent[]) => {
    return components.map(comp => {
      const componentData: any = {};

      switch (comp.type) {
        case 'TITLE':
          componentData[comp.type] = {
            value: comp.data?.title || comp.data?.content || '',
          };
          break;

        case 'PARAGRAPH':
          componentData[comp.type] = {
            value: comp.data?.content || '',
          };
          break;

        case 'IMAGES':
          const items = comp.data?.items || [];
          const processedItems = items.map((item: any) => {
            if (typeof item === 'string') {
              return { url: item };
            }
            return {
              url: item.url || item.mediaId || '',
              name: item.name || '',
            };
          });
          console.log('🖼️ [Form] Processed items:', processedItems);
          componentData[comp.type] = {
            items: processedItems,
          };
          break;

        case 'LOGO':
          componentData[comp.type] = {
            value: comp.data?.value || '',
          };
          break;

        case 'BUTTONS':
          componentData[comp.type] = {
            items: (comp.data?.items || comp.data?.buttons || []).map((button: any) => ({
              type: button.type || 1,
              title: button.title || '',
              content: button.content || button.url || '',
            })),
          };
          break;

        case 'TABLE':
          componentData[comp.type] = {
            rows: comp.data?.rows || [],
          };
          break;

        case 'OTP':
          componentData[comp.type] = {
            value: comp.data?.content || comp.data?.value || '',
          };
          break;

        case 'VOUCHER':
          componentData[comp.type] = {
            name: comp.data?.name || '',
            condition: comp.data?.condition || '',
            start_date: comp.data?.start_date || '',
            end_date: comp.data?.end_date || '',
            voucher_code: comp.data?.voucher_code || '',
            display_code: comp.data?.display_code || '2',
          };
          break;

        case 'PAYMENT':
          componentData[comp.type] = {
            amount: comp.data?.amount || '',
            currency: comp.data?.currency || 'VND',
            description: comp.data?.description || '',
          };
          break;

        case 'RATING':
          componentData[comp.type] = {
            question: comp.data?.question || '',
            scale: comp.data?.scale || 5,
          };
          break;

        default:
          componentData[comp.type] = comp.data || {};
      }

      return componentData;
    });
  };

  // Convert form data to API format
  const convertToApiFormat = (): any => {
    const headerComponents = formData.components.filter(comp => ['IMAGES'].includes(comp.type));
    const bodyComponents = formData.components.filter(comp =>
      ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(comp.type)
    );
    const footerComponents = formData.components.filter(comp => ['BUTTONS'].includes(comp.type));

    const layout: any = {};

    if (headerComponents.length > 0) {
      layout.header = {
        components: convertComponentsToApiFormat(headerComponents),
      };
    }

    layout.body = {
      components: convertComponentsToApiFormat(bodyComponents),
    };

    if (footerComponents.length > 0) {
      layout.footer = {
        components: convertComponentsToApiFormat(footerComponents),
      };
    }

    const apiData = {
      template_name: formData.template_name,
      template_type: parseInt(formData.templateType),
      tag: getTagNumber(formData.tag),
      layout: layout,
      params: extractedParameters.map(param => ({
        name: param.name,
        type: getParameterTypeNumber(parameterValues[param.name]?.type || param.type),
        sample_value: parameterValues[param.name]?.value || param.sample_value,
      })),
      tracking_id: `template_edit_${Date.now()}`,
      note: formData.note || '',
    };

    return apiData;
  };

  // Submit form
  const handleSubmit = useCallback(async () => {
    try {
      if (!formData.template_name.trim()) {
        showError({
          message: t('marketing:zalo.zns.template.form.validation.templateNameRequired'),
        });
        return;
      }

      if (!formData.templateType) {
        showError({
          message: t('marketing:zalo.zns.template.form.validation.templateTypeRequired'),
        });
        return;
      }

      if (formData.components.length === 0) {
        showError({ message: t('marketing:zalo.zns.template.form.validation.componentsRequired') });
        return;
      }

      const apiData = convertToApiFormat();

      await editMutation.mutateAsync({
        integrationId,
        templateId: template.id.toString(),
        data: apiData,
      });

      showSuccess({ message: t('marketing:zalo.zns.template.form.success.updated') });
      onSuccess?.();
    } catch (error) {
      console.error('Error updating template:', error);
      showError({ message: t('marketing:zalo.zns.template.form.error.updateFailed') });
    }
  }, [
    formData,
    extractedParameters,
    parameterValues,
    editMutation,
    template.id,
    integrationId,
    showSuccess,
    showError,
    onSuccess,
    t,
    convertToApiFormat,
  ]);

  // Step 1: Basic Info
  const renderBasicInfoStep = () => (
    <div className="space-y-6 p-4">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.fields.officialAccount.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.selectedOAId}
            onChange={value => updateFormData(prev => ({ ...prev, selectedOAId: value as string }))}
            options={oaAccounts.map(oa => ({
              value: oa.id,
              label: `${oa.name} (${oa.oaId})`,
            }))}
            placeholder={t('marketing:zalo.zns.template.form.fields.officialAccount.placeholder')}
            loading={isLoadingOAs}
            disabled={true} // Disabled for edit form
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.fields.templateName.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.template_name}
            onChange={e => updateFormData(prev => ({ ...prev, template_name: e.target.value }))}
            placeholder={t('marketing:zalo.zns.template.form.fields.templateName.placeholder')}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.fields.contentType.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.tag}
            onChange={value => updateFormData(prev => ({ ...prev, tag: value as string }))}
            options={[
              {
                value: 'TRANSACTION',
                label: t('marketing:zalo.zns.template.form.fields.contentType.options.transaction'),
              },
              {
                value: 'CUSTOMER_CARE',
                label: t(
                  'marketing:zalo.zns.template.form.fields.contentType.options.customerCare'
                ),
              },
              {
                value: 'PROMOTION',
                label: t('marketing:zalo.zns.template.form.fields.contentType.options.promotion'),
              },
              {
                value: 'OTP',
                label: t('marketing:zalo.zns.template.form.fields.contentType.options.otp'),
              },
            ]}
            placeholder={t('marketing:zalo.zns.template.form.fields.contentType.placeholder')}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.fields.note.label')}
          </label>
          <Textarea
            value={formData.note || ''}
            onChange={e => updateFormData(prev => ({ ...prev, note: e.target.value }))}
            placeholder={t('marketing:zalo.zns.template.form.fields.note.placeholder')}
            rows={3}
          />
        </div>
      </div>
    </div>
  );

  // Step 2: Components
  const renderComponentsStep = () => (
    <div className="space-y-4 lg:space-y-6 p-4">
      <div className="mb-6">
        <Typography variant="h6" className="mb-4">
          {t('marketing:zalo.zns.template.form.steps.components.selectType')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, lg: 3, xl: 4 }} gap={4}>
          {templateTypes.map(type => {
            const isSelected = formData.templateType === type.id;
            return (
              <Card
                key={type.id}
                className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                  isSelected
                    ? 'border-2 border-primary bg-primary/5 shadow-md'
                    : 'border border-gray-200 hover:border-primary/50'
                }`}
                onClick={() => handleTemplateTypeSelect(type.id)}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div
                    className={`p-3 rounded-full ${
                      isSelected ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    <Icon name={type.icon} size="lg" />
                  </div>
                  <Typography
                    variant="body2"
                    className={`font-medium ${isSelected ? 'text-primary' : 'text-gray-700'}`}
                  >
                    {t(type.name)}
                  </Typography>
                </div>
              </Card>
            );
          })}
        </ResponsiveGrid>
      </div>

      {formData.templateType && (
        <div className="flex flex-col xl:flex-row gap-6">
          <div className="flex-1">
            <Card className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('marketing:zalo.zns.template.form.steps.components.designContent')}
              </Typography>
              <div className="h-[500px] overflow-y-auto border rounded-lg p-2">
                <ZNSComponentSelector
                  key={`component-selector-${formData.templateType}`}
                  onComponentsChange={handleComponentsChange}
                  initialComponents={formData.components}
                  templateType={formData.templateType}
                  integrationId={formData.selectedOAId || integrationId}
                  onValidationChange={handleValidationChange}
                />
              </div>

              {showValidation && !componentValidation.isValid && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <Typography variant="body2" className="text-red-600 font-medium mb-2">
                    {t('marketing:zalo.zns.template.form.validation.incompleteTemplate')}
                  </Typography>
                  <ul className="text-sm text-red-500 space-y-1">
                    {componentValidation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </Card>
          </div>

          <div className="xl:w-80">
            <ZNSTemplatePreviewSidebar
              components={formData.components}
              templateType={formData.templateType}
              isOpen={true}
            />
          </div>
        </div>
      )}
    </div>
  );

  // Step 3: Review
  const renderReviewStep = () => (
    <div className="space-y-6 p-4">
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          {t('marketing:zalo.zns.template.form.steps.review.title')}
        </Typography>
        <div className="space-y-3">
          <div>
            <Typography variant="body2" className="font-medium">
              {t('marketing:zalo.zns.template.form.fields.templateName.label')}:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.template_name}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              {t('marketing:zalo.zns.template.form.fields.contentType.label')}:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.tag === 'TRANSACTION' &&
                t('marketing:zalo.zns.template.form.fields.contentType.options.transaction')}
              {formData.tag === 'CUSTOMER_CARE' &&
                t('marketing:zalo.zns.template.form.fields.contentType.options.customerCare')}
              {formData.tag === 'PROMOTION' &&
                t('marketing:zalo.zns.template.form.fields.contentType.options.promotion')}
              {formData.tag === 'OTP' &&
                t('marketing:zalo.zns.template.form.fields.contentType.options.otp')}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              {t('marketing:zalo.zns.template.form.steps.components.selectType')}:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {templateTypes.find(t => t.id === formData.templateType)?.name &&
                t(templateTypes.find(t => t.id === formData.templateType)!.name)}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              {t('marketing:zalo.zns.template.form.steps.review.componentCount')}:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.components.length}
            </Typography>
          </div>
          {formData.note && (
            <div>
              <Typography variant="body2" className="font-medium">
                {t('marketing:zalo.zns.template.form.fields.note.label')}:
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {formData.note}
              </Typography>
            </div>
          )}
        </div>
      </Card>

      <div className="flex flex-col xl:flex-row gap-6">
        <div className="flex-1">
          <Card className="p-4">
            <Typography variant="h6" className="mb-4">
              {t('marketing:zalo.zns.template.form.steps.review.preview')}
            </Typography>
            <div className="h-[400px] overflow-y-auto border rounded-lg p-2">
              <ZNSComponentSelector
                onComponentsChange={() => {}}
                initialComponents={formData.components}
                templateType={formData.templateType}
                integrationId={formData.selectedOAId || integrationId}
                onValidationChange={() => {}}
              />
            </div>
          </Card>
        </div>

        <div className="xl:w-80">
          <ZNSTemplatePreviewSidebar
            components={formData.components}
            templateType={formData.templateType}
            isOpen={true}
          />
        </div>
      </div>
    </div>
  );

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderComponentsStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  };

  return (
    <ZNSAccordionProvider>
      <Card className="">
        {/* Main Content */}
        <div className="flex-1 flex flex-col w-full">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <Typography variant="h5" className="mb-1">
                {t('marketing:zalo.zns.edit.title', 'Chỉnh sửa ZNS Template')}
              </Typography>
            </div>
            <IconCard
              icon="x"
              variant="ghost"
              size="md"
              title={t('marketing:zalo.zns.template.form.actions.close')}
              onClick={onCancel}
            />
          </div>
          <div className=" px-6 mt-2">
            <Stepper
              steps={steps}
              currentStep={currentStep - 1}
              variant="filled"
              size="lg"
              showStepIcons
              colorScheme="primary"
              className=""
            />
          </div>

          {/* Step Content */}
          <div className="flex-1 overflow-y-auto p-3 lg:p-6">{renderStepContent()}</div>

          {/* Footer */}
          <div className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <IconCard
                  icon="arrow-left"
                  variant="default"
                  size="md"
                  title={t('marketing:zalo.zns.template.form.actions.back')}
                  onClick={handlePrevious}
                />
              )}
            </div>

            <div className="flex gap-3">
              <IconCard
                icon="x"
                variant="secondary"
                size="md"
                title={t('marketing:zalo.zns.template.form.actions.cancel')}
                onClick={onCancel}
              />

              {currentStep < 3 ? (
                <IconCard
                  icon="check"
                  variant="primary"
                  size="md"
                  title={t('marketing:zalo.zns.template.form.actions.continue')}
                  onClick={handleNext}
                  disabled={!canProceedFromStep(currentStep)}
                />
              ) : (
                <IconCard
                  icon="save"
                  variant="primary"
                  size="md"
                  title={
                    editMutation.isPending
                      ? t('marketing:zalo.zns.template.form.actions.updating')
                      : t('marketing:zalo.zns.template.form.actions.update')
                  }
                  onClick={handleSubmit}
                  disabled={editMutation.isPending}
                />
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Template Change Confirmation Modal */}
      <Modal
        isOpen={showTemplateChangeModal}
        onClose={cancelTemplateTypeChange}
        title={t('marketing:zalo.zns.componentSelector.templateChange.modal.title')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="primary" onClick={confirmTemplateTypeChange}>
              {t('marketing:zalo.zns.componentSelector.templateChange.modal.confirm')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon name="alert-triangle" className="text-amber-500" size="lg" />
            </div>
            <div className="flex-1">
              <Typography variant="body1" className="font-medium mb-2">
                {t('marketing:zalo.zns.componentSelector.templateChange.modal.warning')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:zalo.zns.componentSelector.templateChange.modal.description')}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </ZNSAccordionProvider>
  );
};

export default EditZNSTemplateForm;
