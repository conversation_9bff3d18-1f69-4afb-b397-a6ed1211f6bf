/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-case-declarations */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Input,
  Select,
  Card,
  Icon,
  ResponsiveGrid,
  Stepper,
  IconCard,
  Modal,
  Button,
  Textarea,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import ZNSComponentSelector from './components/ZNSComponentSelector';
import ZNSTemplatePreviewSidebar from './components/ZNSTemplatePreviewSidebar';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';
import { ZaloService } from '../../services/zalo.service';

// Parameter data type
interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

interface ZaloOfficialAccount {
  id: string;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
}

import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useTranslation } from 'react-i18next';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: ZNSComponent[];
  note?: string;
  selectedOAId?: string;
}

interface ZNSComponent {
  id: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  data: any;
}

interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

const templateTypes = [
  {
    id: '1',
    name: 'ZNS tùy chỉnh',
    icon: 'settings',
  },
  {
    id: '2',
    name: 'ZNS xác thực',
    icon: 'shield',
  },
  {
    id: '3',
    name: 'ZNS yêu cầu thanh toán',
    icon: 'credit-card',
  },
  {
    id: '4',
    name: 'ZNS voucher',
    icon: 'gift',
  },
  {
    id: '5',
    name: 'ZNS đánh giá dịch vụ',
    icon: 'star',
  },
];

// Helper function to map tag values
const mapTagToString = (tag: any): string => {
  if (typeof tag === 'string') return tag;

  const tagMap: { [key: string]: string } = {
    '1': 'TRANSACTION',
    '2': 'CUSTOMER_CARE',
    '3': 'PROMOTION',
    '4': 'OTP',
  };

  return tagMap[tag?.toString()] || 'TRANSACTION';
};

const getTagNumber = (tag: string): string => {
  const tagMap: { [key: string]: string } = {
    TRANSACTION: '1',
    CUSTOMER_CARE: '2',
    PROMOTION: '3',
    OTP: '1', // OTP cũng là cấp độ 1
  };
  return tagMap[tag] || '1';
};

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onClose,
  onSuccess,
}) => {
  const { success: showSuccess, error: showError } = useSmartNotification();
  const editMutation = useEditZNSTemplate();
  const { t } = useTranslation(['marketing', 'common']);

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [initialized, setInitialized] = useState(false);
  const [extractedParameters, setExtractedParameters] = useState<ParameterData[]>([]);
  const [parameterValues, setParameterValues] = useState<{
    [key: string]: { type: string; value: string };
  }>({});
  const [officialAccounts, setOfficialAccounts] = useState<ZaloOfficialAccount[]>([]);
  const [isLoadingOAs, setIsLoadingOAs] = useState(false);

  const [, setComponentValidation] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });

  // Modal confirmation state
  const [showTemplateChangeModal, setShowTemplateChangeModal] = useState(false);
  const [pendingTemplateType, setPendingTemplateType] = useState<string | null>(null);

  // Form data - khởi tạo từ template hiện có
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: template.templateName || '',
    tag: '',
    templateType: '',
    components: [],
    note: '',
    selectedOAId: integrationId,
  });

  // Parse existing template data
  useEffect(() => {
    if (initialized) return;

    if (template.templateContent) {
      try {
        const data = JSON.parse(template.templateContent);

        // Set form data from existing template
        setFormData(prev => {
          const newTemplateType = data.template_type?.toString() || '1';
          const newTag = mapTagToString(data.tag) || 'TRANSACTION';
          const newTemplateName = data.template_name || template.templateName || '';
          const newNote = data.note || '';
          const newSelectedOAId = integrationId;

          // So sánh xem có gì thay đổi không, nếu không thì thôi
          if (
            prev.templateType === newTemplateType &&
            prev.tag === newTag &&
            prev.template_name === newTemplateName &&
            prev.note === newNote &&
            prev.selectedOAId === newSelectedOAId
          ) {
            return prev; // Không thay đổi gì cả, tránh render lại
          }

          return {
            ...prev,
            template_name: newTemplateName,
            templateType: newTemplateType,
            tag: newTag,
            note: newNote,
            selectedOAId: newSelectedOAId,
          };
        });
        setInitialized(true);

        // Parse layout back to components
        if (data.layout) {
          const components: ZNSComponent[] = [];
          let componentId = 1;

          // Parse header components (IMAGES)
          if (data.layout.header?.components) {
            data.layout.header.components.forEach((comp: any) => {
              if (comp.IMAGES) {
                components.push({
                  id: `comp-${componentId++}`,
                  type: 'IMAGES',
                  data: { IMAGES: comp.IMAGES },
                });
              }
            });
          }

          // Parse body components
          if (data.layout.body?.components) {
            data.layout.body.components.forEach((comp: any) => {
              Object.keys(comp).forEach(key => {
                if (
                  ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(
                    key
                  )
                ) {
                  components.push({
                    id: `comp-${componentId++}`,
                    type: key as any,
                    data: { [key]: comp[key] },
                  });
                }
              });
            });
          }

          // Parse footer components (BUTTONS)
          if (data.layout.footer?.components) {
            data.layout.footer.components.forEach((comp: any) => {
              if (comp.BUTTONS) {
                components.push({
                  id: `comp-${componentId++}`,
                  type: 'BUTTONS',
                  data: { BUTTONS: comp.BUTTONS },
                });
              }
            });
          }

          setFormData(prev => ({ ...prev, components }));
        }

        // Parse params
        if (data.params) {
          const params: ParameterData[] = data.params.map((param: any) => ({
            name: param.name,
            type: getParameterTypeString(param.type),
            sample_value: param.sample_value,
            display_name: param.name,
            max_length: getMaxLengthForType(param.type),
          }));
          setExtractedParameters(params);
        }
      } catch (error) {
        console.error('❌ [EditZNSTemplateForm] Error parsing template data:', error);
        showError({ message: 'Không thể parse dữ liệu template' });
      }
    }
  }, [template, integrationId, initialized]); // Remove showError from dependency

  // Fetch official accounts when component mounts
  useEffect(() => {
    const fetchOfficialAccounts = async () => {
      setIsLoadingOAs(true);
      try {
        const response = await ZaloService.getPaginatedAccounts({ page: 1, limit: 100 });
        console.log('Official accounts response:', response);

        if (response.code === 200 && response.result?.items) {
          const mappedAccounts = response.result.items.map(item => ({
            id: item.id.toString(),
            oaId: item.oaId,
            name: item.name,
            description: item.description,
            avatarUrl: item.avatarUrl,
            status: item.status,
          }));
          setOfficialAccounts(mappedAccounts);
        } else {
          console.warn('Invalid response format:', response);
        }
      } catch (error) {
        console.error('Error fetching official accounts:', error);
        showError({ message: 'Không thể tải danh sách Official Account' });
      } finally {
        setIsLoadingOAs(false);
      }
    };

    fetchOfficialAccounts();
  }, []); // Remove showError from dependency array

  // Helper functions
  const getParameterTypeString = (typeNumber: number): string => {
    const typeMap: { [key: number]: string } = {
      1: 'customer',
      2: 'phone',
      3: 'address',
      4: 'id',
      5: 'personal',
      6: 'status',
      7: 'contact',
      8: 'time',
      9: 'product',
      10: 'amount',
      11: 'duration',
      12: 'otp',
      13: 'url',
      14: 'money',
      15: 'bank_note',
    };
    return typeMap[typeNumber] || 'customer';
  };

  const getMaxLengthForType = (typeNumber: number): number => {
    const lengthMap: { [key: number]: number } = {
      1: 30,
      2: 15,
      3: 200,
      4: 30,
      5: 30,
      6: 30,
      7: 50,
      8: 5,
      9: 200,
      10: 20,
      11: 20,
      12: 10,
      13: 200,
      14: 12,
      15: 90,
    };
    return lengthMap[typeNumber] || 30;
  };

  // Steps configuration
  const steps = [
    {
      id: 'basic-info',
      title: 'Thông tin cơ bản',
      icon: 'file-text',
    },
    {
      id: 'components',
      title: 'Thiết kế nội dung',
      icon: 'layers',
    },
    {
      id: 'review',
      title: 'Xem lại & Lưu',
      icon: 'save',
    },
  ];

  // Handle template type change with confirmation
  const handleTemplateTypeChange = useCallback((newTemplateType: string) => {
    setFormData(prev => {
      // If no current template type or no components, change directly
      if (!prev.templateType || prev.components.length === 0) {
        console.log('🔄 [EditZNSTemplateForm] Direct template type change to:', newTemplateType);
        return {
          ...prev,
          templateType: newTemplateType,
          components: [],
        };
      }

      // If changing to same type, do nothing
      if (prev.templateType === newTemplateType) {
        return prev;
      }

      // Show confirmation modal
      console.log(
        '🔄 [EditZNSTemplateForm] Requesting template type change from',
        prev.templateType,
        'to',
        newTemplateType
      );
      setPendingTemplateType(newTemplateType);
      setShowTemplateChangeModal(true);
      return prev; // No change yet
    });
  }, []); // ✅ Empty dependency array

  // Confirm template type change
  const confirmTemplateTypeChange = useCallback(() => {
    if (pendingTemplateType) {
      console.log(
        '🔄 [EditZNSTemplateForm] Confirmed template type change to:',
        pendingTemplateType
      );

      setFormData(prev => {
        const newData = {
          ...prev,
          templateType: pendingTemplateType,
          components: [],
        };
        console.log(
          '🔄 [EditZNSTemplateForm] After update - new templateType:',
          newData.templateType
        );
        return newData;
      });
    }
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, [pendingTemplateType]); // ✅ Bỏ formData.templateType

  // Cancel template type change
  const cancelTemplateTypeChange = useCallback(() => {
    console.log('🔄 [EditZNSTemplateForm] Cancelled template type change');
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, []);

  // Helper functions for parameter metadata
  const getParameterType = (paramName: string): string => {
    const lowerName = paramName.toLowerCase();

    // Tên khách hàng
    if (lowerName.includes('name') || lowerName.includes('customer') || lowerName.includes('ten'))
      return 'customer';
    // Số điện thoại
    if (lowerName.includes('phone') || lowerName.includes('sdt') || lowerName.includes('dienthoai'))
      return 'phone';
    // Địa chỉ
    if (lowerName.includes('address') || lowerName.includes('diachi')) return 'address';
    // ID/Mã
    if (lowerName.includes('id') || lowerName.includes('code') || lowerName.includes('ma'))
      return 'id';
    // Thông tin cá nhân
    if (lowerName.includes('personal') || lowerName.includes('canhan')) return 'personal';
    // Trạng thái
    if (lowerName.includes('status') || lowerName.includes('trangthai')) return 'status';
    // Liên hệ
    if (lowerName.includes('contact') || lowerName.includes('lienhe')) return 'contact';
    // Thời gian
    if (lowerName.includes('time') || lowerName.includes('date') || lowerName.includes('thoigian'))
      return 'time';
    // Sản phẩm
    if (lowerName.includes('product') || lowerName.includes('sanpham')) return 'product';
    // Số lượng
    if (lowerName.includes('amount') || lowerName.includes('soluong')) return 'amount';
    // Thời hạn
    if (lowerName.includes('duration') || lowerName.includes('thoihan')) return 'duration';
    // OTP
    if (lowerName.includes('otp')) return 'otp';
    // URL
    if (lowerName.includes('url') || lowerName.includes('link')) return 'url';
    // Tiền
    if (lowerName.includes('money') || lowerName.includes('tien')) return 'money';
    // Ghi chú ngân hàng
    if (lowerName.includes('bank') || lowerName.includes('nganhang')) return 'bank_note';

    return 'personal'; // Default
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const getSampleValue = (paramName: string): string => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return t('marketing:zalo.zns.parameters.sampleValues.customer');
      case 'phone':
        return t('marketing:zalo.zns.parameters.sampleValues.phone');
      case 'address':
        return t('marketing:zalo.zns.parameters.sampleValues.address');
      case 'id':
        return t('marketing:zalo.zns.parameters.sampleValues.id');
      case 'personal':
        return t('marketing:zalo.zns.parameters.sampleValues.personal');
      case 'status':
        return t('marketing:zalo.zns.parameters.sampleValues.status');
      case 'contact':
        return t('marketing:zalo.zns.parameters.sampleValues.contact');
      case 'time':
        return t('marketing:zalo.zns.parameters.sampleValues.time');
      case 'product':
        return t('marketing:zalo.zns.parameters.sampleValues.product');
      case 'amount':
        return t('marketing:zalo.zns.parameters.sampleValues.amount');
      case 'duration':
        return t('marketing:zalo.zns.parameters.sampleValues.duration');
      case 'otp':
        return t('marketing:zalo.zns.parameters.sampleValues.otp');
      case 'url':
        return t('marketing:zalo.zns.parameters.sampleValues.url');
      case 'money':
        return t('marketing:zalo.zns.parameters.sampleValues.money');
      case 'bank_note':
        return t('marketing:zalo.zns.parameters.sampleValues.bank_note');
      default:
        return t('marketing:zalo.zns.parameters.sampleValues.default', { paramName });
    }
  };

  const getDisplayName = (paramName: string): string => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return t('marketing:zalo.zns.parameters.displayNames.customer');
      case 'phone':
        return t('marketing:zalo.zns.parameters.displayNames.phone');
      case 'address':
        return t('marketing:zalo.zns.parameters.displayNames.address');
      case 'id':
        return t('marketing:zalo.zns.parameters.displayNames.id');
      case 'personal':
        return t('marketing:zalo.zns.parameters.displayNames.personal');
      case 'status':
        return t('marketing:zalo.zns.parameters.displayNames.status');
      case 'contact':
        return t('marketing:zalo.zns.parameters.displayNames.contact');
      case 'time':
        return t('marketing:zalo.zns.parameters.displayNames.time');
      case 'product':
        return t('marketing:zalo.zns.parameters.displayNames.product');
      case 'amount':
        return t('marketing:zalo.zns.parameters.displayNames.amount');
      case 'duration':
        return t('marketing:zalo.zns.parameters.displayNames.duration');
      case 'otp':
        return t('marketing:zalo.zns.parameters.displayNames.otp');
      case 'url':
        return t('marketing:zalo.zns.parameters.displayNames.url');
      case 'money':
        return t('marketing:zalo.zns.parameters.displayNames.money');
      case 'bank_note':
        return t('marketing:zalo.zns.parameters.displayNames.bank_note');
      default:
        return paramName;
    }
  };

  const getMaxLength = (paramName: string): number => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return 50;
      case 'phone':
        return 15;
      case 'address':
        return 100;
      case 'id':
        return 30;
      case 'personal':
        return 50;
      case 'status':
        return 30;
      case 'contact':
        return 50;
      case 'time':
        return 30;
      case 'product':
        return 50;
      case 'amount':
        return 20;
      case 'duration':
        return 20;
      case 'otp':
        return 10;
      case 'url':
        return 200;
      case 'money':
        return 20;
      case 'bank_note':
        return 100;
      default:
        return 50;
    }
  };

  // Extract parameters from components
  const extractParametersFromComponents = useCallback(
    (components: ZNSComponent[]): ParameterData[] => {
      const parameters: ParameterData[] = [];
      const paramRegex = /<([^>]+)>/g;
      const foundParams = new Set<string>();

      components.forEach(component => {
        let textContent = '';

        // Extract text content based on component type
        switch (component.type) {
          case 'TITLE':
            textContent = component.data?.title || component.data?.content || '';
            break;
          case 'PARAGRAPH':
            textContent = component.data?.content || '';
            break;
          case 'TABLE':
            if (component.data?.rows) {
              component.data.rows.forEach((row: any) => {
                if (row.cells) {
                  row.cells.forEach((cell: any) => {
                    textContent += cell.content || '';
                  });
                }
              });
            }
            break;
          case 'OTP':
            textContent = component.data?.content || component.data?.value || '';
            if (!foundParams.has('otp')) {
              foundParams.add('otp');
              parameters.push({
                name: 'otp',
                type: 'otp',
                sample_value: '123456',
                display_name: 'Mã xác thực OTP',
                max_length: 10,
              });
            }
            break;
          case 'VOUCHER':
            textContent +=
              (component.data?.name || '') +
              (component.data?.condition || '') +
              (component.data?.voucher_code || '');
            break;
          case 'PAYMENT':
            textContent += component.data?.description || '';
            if (!foundParams.has('amount')) {
              foundParams.add('amount');
              parameters.push({
                name: 'amount',
                type: 'amount',
                sample_value: '500000',
                display_name: 'Số tiền',
                max_length: 20,
              });
            }
            break;
        }

        // Find parameters in text content
        let match;
        paramRegex.lastIndex = 0;
        while ((match = paramRegex.exec(textContent)) !== null) {
          const paramName = match[1];
          if (!foundParams.has(paramName)) {
            foundParams.add(paramName);
            parameters.push({
              name: paramName,
              type: getParameterType(paramName),
              sample_value: getSampleValue(paramName),
              display_name: getDisplayName(paramName),
              max_length: getMaxLength(paramName),
            });
          }
        }
      });

      return parameters;
    },
    [getParameterType, getSampleValue, getDisplayName, getMaxLength]
  );

  // Handle components change
  const handleComponentsChange = useCallback(
    (components: ZNSComponent[]) => {
      console.log('🔄 [EditZNSTemplateForm] Components changed:', components?.length);
      setFormData(prev => ({
        ...prev,
        components: components || [],
      }));

      // Extract parameters when components change
      const parameters = extractParametersFromComponents(components);
      setExtractedParameters(prev => {
        // Chỉ update khi thực sự có thay đổi
        if (JSON.stringify(prev) === JSON.stringify(parameters)) {
          return prev;
        }
        return parameters;
      });

      // Initialize parameter values
      const newParameterValues: { [key: string]: { type: string; value: string } } = {};
      parameters.forEach(param => {
        newParameterValues[param.name] = {
          type: param.type,
          value: param.sample_value,
        };
      });
      setParameterValues(newParameterValues);
    },
    [extractParametersFromComponents]
  );

  // Handle validation change
  const handleValidationChange = useCallback((isValid: boolean, errors: string[]) => {
    setComponentValidation({ isValid, errors });
  }, []);

  // Generate random tracking ID
  const generateTrackingId = (): string => {
    return `template_edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // Convert to API format (similar to AddForm)
  const convertToApiFormat = (): any => {
    // Group components by section
    const headerComponents = formData.components.filter(comp => ['IMAGES'].includes(comp.type));
    const bodyComponents = formData.components.filter(comp =>
      ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(comp.type)
    );
    const footerComponents = formData.components.filter(comp => ['BUTTONS'].includes(comp.type));

    const layout: any = {};

    // Add header if has header components
    if (headerComponents.length > 0) {
      layout.header = {
        components: convertComponentsToApiFormat(headerComponents),
      };
    }

    // Always add body
    layout.body = {
      components: convertComponentsToApiFormat(bodyComponents),
    };

    // Add footer if has footer components
    if (footerComponents.length > 0) {
      layout.footer = {
        components: convertComponentsToApiFormat(footerComponents),
      };
    }

    const apiData = {
      template_name: formData.template_name,
      template_type: parseInt(formData.templateType),
      tag: getTagNumber(formData.tag),
      layout: layout,
      params: extractedParameters.map(param => ({
        name: param.name,
        type: getParameterTypeNumber(parameterValues[param.name]?.type || param.type),
        sample_value: parameterValues[param.name]?.value || param.sample_value,
      })),
      tracking_id: generateTrackingId(),
      note: formData.note || '',
    };

    return apiData;
  };

  // Convert components to API format
  const convertComponentsToApiFormat = (components: ZNSComponent[]) => {
    return components.map(comp => {
      const componentData: any = {};
      componentData[comp.type] = comp.data[comp.type] || comp.data;
      return componentData;
    });
  };

  // Get parameter type number
  const getParameterTypeNumber = (type: string): number => {
    const typeMap: { [key: string]: number } = {
      customer: 1,
      phone: 2,
      address: 3,
      id: 4,
      personal: 5,
      status: 6,
      contact: 7,
      time: 8,
      product: 9,
      amount: 10,
      duration: 11,
      otp: 12,
      url: 13,
      money: 14,
      bank_note: 15,
    };
    return typeMap[type] || 1;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.selectedOAId) {
        showError({ message: 'Vui lòng chọn Official Account' });
        return;
      }

      if (!formData.template_name.trim()) {
        showError({ message: 'Tên template là bắt buộc' });
        return;
      }

      if (!formData.templateType) {
        showError({ message: 'Loại template là bắt buộc' });
        return;
      }

      if (formData.components.length === 0) {
        showError({ message: 'Template phải có ít nhất 1 component' });
        return;
      }

      const apiData = convertToApiFormat();
      console.log('🚀 [EditZNSTemplateForm] Submitting:', JSON.stringify(apiData, null, 2));

      await editMutation.mutateAsync({
        integrationId: formData.selectedOAId || integrationId,
        templateId: template.id.toString(),
        data: apiData,
      });

      showSuccess({ message: 'Chỉnh sửa template thành công!' });
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('❌ [EditZNSTemplateForm] Submit error:', error);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderComponentsStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  };

  // Step 1: Basic Info
  const renderBasicInfoStep = () => (
    <div className="space-y-4 lg:space-y-6 p-4">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Tên mẫu ZNS <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.template_name}
            onChange={e => setFormData(prev => ({ ...prev, template_name: e.target.value }))}
            maxLength={60}
            placeholder="VD: Thông báo xác nhận đơn hàng"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Chọn Official Account <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.selectedOAId}
            onChange={value => setFormData(prev => ({ ...prev, selectedOAId: value as string }))}
            options={officialAccounts.map(oa => ({
              value: oa.id,
              label: oa.name,
            }))}
            placeholder="Chọn Official Account"
            loading={isLoadingOAs}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Chọn loại nội dung ZNS <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.tag}
            onChange={value => setFormData(prev => ({ ...prev, tag: value as string }))}
            options={[
              { value: 'TRANSACTION', label: 'Giao dịch (Cấp độ 1)' },
              { value: 'CUSTOMER_CARE', label: 'Chăm sóc khách hàng (Cấp độ 2)' },
              { value: 'PROMOTION', label: 'Khuyến mãi (Cấp độ 3)' },
              { value: 'OTP', label: 'OTP (Cấp độ 1)' },
            ]}
            placeholder="Chọn loại nội dung ZNS"
          />
        </div>
      </div>
    </div>
  );

  // Step 2: Components
  const renderComponentsStep = () => {
    return (
      <div className="space-y-4 lg:space-y-6 p-4">
        <div className="mb-6">
          <Typography variant="h6" className="mb-4">
            Chọn loại template
          </Typography>

          <ResponsiveGrid maxColumns={{ xs: 2, sm: 2, md: 3 }} gap={4}>
            {templateTypes.map(type => {
              const isSelected =
                formData.templateType === type.id || pendingTemplateType === type.id;

              return (
                <Card
                  key={type.id}
                  className={`p-4 cursor-pointer border-2 transition-all hover:shadow-md ${
                    isSelected
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => handleTemplateTypeChange(type.id)}
                >
                  <div className="flex flex-col items-center text-center space-y-3">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        formData.templateType === type.id
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <Icon name={type.icon as any} size="sm" />
                    </div>
                    <Typography variant="body2" className="font-medium">
                      {type.name}
                    </Typography>
                  </div>
                </Card>
              );
            })}
          </ResponsiveGrid>
        </div>

        {/* Component Selector */}
        {formData.templateType && (
          <div className="flex flex-col xl:flex-row gap-6">
            <div className="flex-1">
              <Card className="p-4">
                <Typography variant="h6" className="mb-4">
                  Thiết kế nội dung
                </Typography>
                <div className="h-[500px] overflow-y-auto border rounded-lg p-2">
                  <ZNSComponentSelector
                    // key={`component-selector-${formData.templateType}`}
                    onComponentsChange={handleComponentsChange}
                    initialComponents={[]}
                    templateType={formData.templateType}
                    integrationId={formData.selectedOAId || integrationId}
                    onValidationChange={handleValidationChange}
                  />
                </div>
              </Card>
            </div>

            <div className="hidden xl:block w-80 flex-shrink-0">
              <div className="sticky top-4">
                <ZNSTemplatePreviewSidebar
                  key={`preview-${formData.templateType}`}
                  templateType={formData.templateType}
                  components={formData.components}
                  isOpen={true}
                  isInline={true}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Step 3: Review
  const renderReviewStep = () => (
    <div className="space-y-4 lg:space-y-6 p-4">
      {/* Template Summary */}
      <Card className="p-3 lg:p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 border border-blue-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="flex items-center justify-center w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded">
            <svg
              className="w-3 h-3 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <Typography
            variant="h6"
            className="text-sm lg:text-base font-medium text-blue-800 dark:text-blue-300"
          >
            Thông tin Template
          </Typography>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
          <div>
            <Typography
              variant="body2"
              className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              Tên Template:
            </Typography>
            <Typography
              variant="body2"
              className="text-sm lg:text-base text-gray-800 dark:text-gray-200"
            >
              {formData.template_name}
            </Typography>
          </div>
          <div>
            <Typography
              variant="body2"
              className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              Loại nội dung:
            </Typography>
            <Typography
              variant="body2"
              className="text-sm lg:text-base text-gray-800 dark:text-gray-200"
            >
              {formData.tag === 'TRANSACTION' && 'Giao dịch (Cấp độ 1)'}
              {formData.tag === 'CUSTOMER_CARE' && 'Chăm sóc khách hàng (Cấp độ 2)'}
              {formData.tag === 'PROMOTION' && 'Khuyến mãi (Cấp độ 3)'}
              {formData.tag === 'OTP' && 'Xác thực OTP (Cấp độ 1)'}
            </Typography>
          </div>
          <div>
            <Typography
              variant="body2"
              className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              Loại Template:
            </Typography>
            <Typography
              variant="body2"
              className="text-sm lg:text-base text-gray-800 dark:text-gray-200"
            >
              {templateTypes.find(type => type.id === formData.templateType)?.name &&
                t(templateTypes.find(type => type.id === formData.templateType)!.name)}
            </Typography>
          </div>
          <div>
            <Typography
              variant="body2"
              className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            >
              Số lượng Component:
            </Typography>
            <Typography
              variant="body2"
              className="text-sm lg:text-base text-gray-800 dark:text-gray-200"
            >
              {formData.components.length}
            </Typography>
          </div>
        </div>
      </Card>

      {/* Parameters Configuration */}
      {extractedParameters.length > 0 && (
        <Card className="p-3 lg:p-4 bg-card dark:bg-gray-800 border-border dark:border-gray-700">
          <Typography
            variant="h5"
            className="mb-3 lg:mb-4 text-base lg:text-lg font-medium text-foreground dark:text-gray-100"
          >
            Tham số Template ({extractedParameters.length})
          </Typography>
          <div className="space-y-3 lg:space-y-4">
            {extractedParameters.map(param => (
              <div
                key={param.name}
                className="p-3 lg:p-4 bg-muted/30 dark:bg-gray-700/30 rounded-lg"
              >
                {/* Mobile Layout - Vertical */}
                <div className="block lg:hidden space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      Tên tham số
                    </label>
                    <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded text-sm font-mono text-center">
                      {param.name}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      Loại dữ liệu
                    </label>
                    <Select
                      value={parameterValues[param.name]?.type || param.type}
                      onChange={value =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            type: value as string,
                          },
                        }))
                      }
                      options={[
                        { value: 'customer', label: 'Tên khách hàng' },
                        { value: 'phone', label: 'Số điện thoại' },
                        { value: 'address', label: 'Địa chỉ' },
                        { value: 'id', label: 'Mã/ID' },
                        { value: 'personal', label: 'Thông tin cá nhân' },
                        { value: 'status', label: 'Trạng thái' },
                        { value: 'contact', label: 'Liên hệ' },
                        { value: 'time', label: 'Thời gian' },
                        { value: 'product', label: 'Sản phẩm' },
                        { value: 'amount', label: 'Số lượng' },
                        { value: 'duration', label: 'Thời hạn' },
                        { value: 'otp', label: 'Mã OTP' },
                        { value: 'url', label: 'Đường dẫn' },
                        { value: 'money', label: 'Tiền tệ' },
                        { value: 'bank_note', label: 'Ghi chú ngân hàng' },
                      ]}
                      size="sm"
                      fullWidth
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      Giá trị mẫu
                    </label>
                    <Input
                      value={parameterValues[param.name]?.value || param.sample_value}
                      onChange={e =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            value: e.target.value,
                          },
                        }))
                      }
                      placeholder="Nhập giá trị mẫu"
                      fullWidth
                    />
                  </div>
                </div>

                {/* Desktop Layout - Horizontal */}
                <div className="hidden lg:grid lg:grid-cols-3 lg:gap-4 lg:items-end">
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      Tên tham số
                    </label>
                    <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded text-sm font-mono text-center">
                      {param.name}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      Loại dữ liệu
                    </label>
                    <Select
                      value={parameterValues[param.name]?.type || param.type}
                      onChange={value =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            type: value as string,
                          },
                        }))
                      }
                      options={[
                        { value: 'customer', label: 'Tên khách hàng' },
                        { value: 'phone', label: 'Số điện thoại' },
                        { value: 'address', label: 'Địa chỉ' },
                        { value: 'id', label: 'Mã/ID' },
                        { value: 'personal', label: 'Thông tin cá nhân' },
                        { value: 'status', label: 'Trạng thái' },
                        { value: 'contact', label: 'Liên hệ' },
                        { value: 'time', label: 'Thời gian' },
                        { value: 'product', label: 'Sản phẩm' },
                        { value: 'amount', label: 'Số lượng' },
                        { value: 'duration', label: 'Thời hạn' },
                        { value: 'otp', label: 'Mã OTP' },
                        { value: 'url', label: 'Đường dẫn' },
                        { value: 'money', label: 'Tiền tệ' },
                        { value: 'bank_note', label: 'Ghi chú ngân hàng' },
                      ]}
                      size="sm"
                      fullWidth
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      Giá trị mẫu
                    </label>
                    <Input
                      value={parameterValues[param.name]?.value || param.sample_value}
                      onChange={e =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            value: e.target.value,
                          },
                        }))
                      }
                      placeholder="Nhập giá trị mẫu"
                      fullWidth
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Note */}
      <Card className="p-3 lg:p-4 bg-card dark:bg-gray-800 border-border dark:border-gray-700">
        <label className="block text-sm lg:text-base font-medium mb-2 lg:mb-3 text-foreground dark:text-gray-100">
          Ghi chú thêm về template
        </label>
        <Textarea
          value={formData.note || ''}
          onChange={e => setFormData(prev => ({ ...prev, note: e.target.value }))}
          placeholder="Ghi chú thêm về template..."
          rows={3}
          fullWidth
          className="text-sm lg:text-base"
        />
      </Card>
    </div>
  );

  return (
    <ZNSAccordionProvider>
      <div className="h-full flex flex-col bg-background">
        {/* Header */}
        <div className="flex-shrink-0 px-4 py-3 border-b border-border bg-card">
          <div className="flex items-center justify-between">
            <Typography variant="h5" className="text-foreground">
              Chỉnh sửa ZNS Template
            </Typography>
            <IconCard
              icon="x"
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            />
          </div>
        </div>

        {/* Stepper */}
        <div className="flex-shrink-0 px-4 py-4 bg-muted/30">
          <Stepper
            steps={steps}
            currentStep={currentStep}
            onStepClick={stepId =>
              setCurrentStep(typeof stepId === 'number' ? stepId : parseInt(stepId.toString()))
            }
            size="sm"
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full p-4">{renderStepContent()}</div>
        </div>

        {/* Footer Actions */}
        <div className="flex-shrink-0 px-4 py-3 border-t border-border bg-card">
          <div className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <IconCard
                  icon="chevron-left"
                  variant="secondary"
                  size="md"
                  title="Quay lại"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                />
              )}
            </div>

            <div className="flex gap-3">
              <IconCard icon="x" variant="secondary" size="md" title="Hủy" onClick={onClose} />

              {currentStep < steps.length ? (
                <IconCard
                  icon="chevron-right"
                  variant="primary"
                  size="md"
                  title="Tiếp theo"
                  onClick={() => setCurrentStep(prev => prev + 1)}
                />
              ) : (
                <IconCard
                  icon="save"
                  variant="primary"
                  size="md"
                  title="Lưu thay đổi"
                  isLoading={editMutation.isPending}
                  onClick={handleSubmit}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Template Change Confirmation Modal */}
      <Modal
        isOpen={showTemplateChangeModal}
        onClose={cancelTemplateTypeChange}
        title="Xác nhận thay đổi template"
        size="sm"
        footer={null}
      >
        <div className="space-y-4">
          <Typography variant="body1" className="text-muted-foreground">
            Bạn có chắc chắn muốn thay đổi loại template không? Tất cả các component hiện tại sẽ bị
            xóa.
          </Typography>

          <div className="flex justify-end gap-3">
            <Button variant="secondary" size="sm" onClick={cancelTemplateTypeChange}>
              Hủy
            </Button>
            <Button variant="primary" size="sm" onClick={confirmTemplateTypeChange}>
              Xác nhận
            </Button>
          </div>
        </div>
      </Modal>
    </ZNSAccordionProvider>
  );
};

export default EditZNSTemplateForm;
