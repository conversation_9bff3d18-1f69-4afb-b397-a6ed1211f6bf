/* eslint-disable no-case-declarations */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Typography,
  Input,
  Select,
  Card,
  Icon,
  ResponsiveGrid,
  Stepper,
  IconCard,
  Modal,
  Button,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import ZNSComponentSelector from './components/ZNSComponentSelector';
import ZNSTemplatePreviewSidebar from './components/ZNSTemplatePreviewSidebar';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: ZNSComponent[];
  note?: string;
  selectedOAId?: string;
}

interface ZNSComponent {
  id: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  data: any;
}

interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

const templateTypes = [
  {
    id: '1',
    name: 'ZNS tùy chỉnh',
    icon: 'settings',
  },
  {
    id: '2',
    name: 'ZNS xác thực',
    icon: 'shield',
  },
  {
    id: '3',
    name: 'ZNS yêu cầu thanh toán',
    icon: 'credit-card',
  },
  {
    id: '4',
    name: 'ZNS voucher',
    icon: 'gift',
  },
  {
    id: '5',
    name: 'ZNS đánh giá dịch vụ',
    icon: 'star',
  },
];

// Helper function to map tag values
const mapTagToString = (tag: any): string => {
  if (typeof tag === 'string') return tag;

  const tagMap: { [key: string]: string } = {
    '1': 'TRANSACTION',
    '2': 'CUSTOMER_CARE',
    '3': 'PROMOTION',
    '4': 'OTP',
  };

  return tagMap[tag?.toString()] || 'TRANSACTION';
};

const mapTagToNumber = (tag: string): number => {
  const tagMap: { [key: string]: number } = {
    TRANSACTION: 1,
    CUSTOMER_CARE: 2,
    PROMOTION: 3,
    OTP: 4,
  };

  return tagMap[tag] || 1;
};

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onClose,
  onSuccess,
}) => {
  const { success: showSuccess, error: showError } = useSmartNotification();
  const editMutation = useEditZNSTemplate();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [extractedParameters, setExtractedParameters] = useState<ParameterData[]>([]);
  const [parameterValues] = useState<{
    [key: string]: { type: string; value: string };
  }>({});
  const [, setComponentValidation] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });

  // Modal confirmation state
  const [showTemplateChangeModal, setShowTemplateChangeModal] = useState(false);
  const [pendingTemplateType, setPendingTemplateType] = useState<string | null>(null);

  // Form data - khởi tạo từ template hiện có
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: template.templateName || '',
    tag: '',
    templateType: '',
    components: [],
    note: '',
    selectedOAId: integrationId,
  });

  // Parse existing template data
  useEffect(() => {
    if (template.templateContent) {
      try {
        const data = JSON.parse(template.templateContent);

        // Set form data from existing template
        setFormData(prev => {
          const newTemplateType = data.template_type?.toString() || '1';
          const newTag = mapTagToString(data.tag) || 'TRANSACTION';
          const newTemplateName = data.template_name || template.templateName || '';
          const newNote = data.note || '';
          const newSelectedOAId = integrationId;

          // So sánh xem có gì thay đổi không, nếu không thì thôi
          if (
            prev.templateType === newTemplateType &&
            prev.tag === newTag &&
            prev.template_name === newTemplateName &&
            prev.note === newNote &&
            prev.selectedOAId === newSelectedOAId
          ) {
            return prev; // Không thay đổi gì cả, tránh render lại
          }

          return {
            ...prev,
            template_name: newTemplateName,
            templateType: newTemplateType,
            tag: newTag,
            note: newNote,
            selectedOAId: newSelectedOAId,
          };
        });

        // Parse layout back to components
        if (data.layout) {
          const components: ZNSComponent[] = [];
          let componentId = 1;

          // Parse header components (IMAGES)
          if (data.layout.header?.components) {
            data.layout.header.components.forEach((comp: any) => {
              if (comp.IMAGES) {
                components.push({
                  id: `comp-${componentId++}`,
                  type: 'IMAGES',
                  data: { IMAGES: comp.IMAGES },
                });
              }
            });
          }

          // Parse body components
          if (data.layout.body?.components) {
            data.layout.body.components.forEach((comp: any) => {
              Object.keys(comp).forEach(key => {
                if (
                  ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(
                    key
                  )
                ) {
                  components.push({
                    id: `comp-${componentId++}`,
                    type: key as any,
                    data: { [key]: comp[key] },
                  });
                }
              });
            });
          }

          // Parse footer components (BUTTONS)
          if (data.layout.footer?.components) {
            data.layout.footer.components.forEach((comp: any) => {
              if (comp.BUTTONS) {
                components.push({
                  id: `comp-${componentId++}`,
                  type: 'BUTTONS',
                  data: { BUTTONS: comp.BUTTONS },
                });
              }
            });
          }

          setFormData(prev => ({ ...prev, components }));
        }

        // Parse params
        if (data.params) {
          const params: ParameterData[] = data.params.map((param: any) => ({
            name: param.name,
            type: getParameterTypeString(param.type),
            sample_value: param.sample_value,
            display_name: param.name,
            max_length: getMaxLengthForType(param.type),
          }));
          setExtractedParameters(params);
        }
      } catch (error) {
        console.error('❌ [EditZNSTemplateForm] Error parsing template data:', error);
        showError({ message: 'Không thể parse dữ liệu template' });
      }
    }
  }, [template, integrationId, showError]);

  // Helper functions
  const getParameterTypeString = (typeNumber: number): string => {
    const typeMap: { [key: number]: string } = {
      1: 'customer',
      2: 'phone',
      3: 'address',
      4: 'id',
      5: 'personal',
      6: 'status',
      7: 'contact',
      8: 'time',
      9: 'product',
      10: 'amount',
      11: 'duration',
      12: 'otp',
      13: 'url',
      14: 'money',
      15: 'bank_note',
    };
    return typeMap[typeNumber] || 'customer';
  };

  const getMaxLengthForType = (typeNumber: number): number => {
    const lengthMap: { [key: number]: number } = {
      1: 30,
      2: 15,
      3: 200,
      4: 30,
      5: 30,
      6: 30,
      7: 50,
      8: 5,
      9: 200,
      10: 20,
      11: 20,
      12: 10,
      13: 200,
      14: 12,
      15: 90,
    };
    return lengthMap[typeNumber] || 30;
  };

  // Steps configuration
  const steps = [
    {
      id: 'basic-info',
      title: 'Thông tin cơ bản',
      icon: 'file-text',
    },
    {
      id: 'components',
      title: 'Thiết kế nội dung',
      icon: 'layers',
    },
    {
      id: 'review',
      title: 'Xem lại & Lưu',
      icon: 'save',
    },
  ];

  // Update form data helper
  const updateFormData = useCallback((updater: (prev: TemplateFormData) => TemplateFormData) => {
    setFormData(updater);
  }, []);

  // Handle template type change with confirmation
  const handleTemplateTypeChange = useCallback(
    (newTemplateType: string) => {
      // If no current template type or no components, change directly
      if (!formData.templateType || formData.components.length === 0) {
        console.log('🔄 [EditZNSTemplateForm] Direct template type change to:', newTemplateType);
        setFormData(prev => ({
          ...prev,
          templateType: newTemplateType,
          components: [],
        }));
        return;
      }

      // If changing to same type, do nothing
      if (formData.templateType === newTemplateType) {
        return;
      }

      // Show confirmation modal
      console.log(
        '🔄 [EditZNSTemplateForm] Requesting template type change from',
        formData.templateType,
        'to',
        newTemplateType
      );
      setPendingTemplateType(newTemplateType);
      setShowTemplateChangeModal(true);
    },
    [formData.templateType, formData.components.length] // ✅ Bỏ updateFormData
  );

  // Confirm template type change
  const confirmTemplateTypeChange = useCallback(() => {
    if (pendingTemplateType) {
      console.log(
        '🔄 [EditZNSTemplateForm] Confirmed template type change to:',
        pendingTemplateType
      );

      setFormData(prev => {
        const newData = {
          ...prev,
          templateType: pendingTemplateType,
          components: [],
        };
        console.log(
          '🔄 [EditZNSTemplateForm] After update - new templateType:',
          newData.templateType
        );
        return newData;
      });
    }
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, [pendingTemplateType]); // ✅ Bỏ formData.templateType

  // Cancel template type change
  const cancelTemplateTypeChange = useCallback(() => {
    console.log('🔄 [EditZNSTemplateForm] Cancelled template type change');
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, []);

  // Handle components change
  const handleComponentsChange = useCallback(
    (components: ZNSComponent[]) => {
      console.log('🔄 [EditZNSTemplateForm] Components changed:', components?.length);
      updateFormData(prev => ({
        ...prev,
        components: components || [],
      }));
    },
    [updateFormData]
  );

  // Handle validation change
  const handleValidationChange = useCallback((isValid: boolean, errors: string[]) => {
    setComponentValidation({ isValid, errors });
  }, []);

  // Convert to API format (similar to AddForm)
  const convertToApiFormat = (): any => {
    // Group components by section
    const headerComponents = formData.components.filter(comp => ['IMAGES'].includes(comp.type));
    const bodyComponents = formData.components.filter(comp =>
      ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(comp.type)
    );
    const footerComponents = formData.components.filter(comp => ['BUTTONS'].includes(comp.type));

    const layout: any = {};

    // Add header if has header components
    if (headerComponents.length > 0) {
      layout.header = {
        components: convertComponentsToApiFormat(headerComponents),
      };
    }

    // Always add body
    layout.body = {
      components: convertComponentsToApiFormat(bodyComponents),
    };

    // Add footer if has footer components
    if (footerComponents.length > 0) {
      layout.footer = {
        components: convertComponentsToApiFormat(footerComponents),
      };
    }

    const apiData = {
      template_name: formData.template_name,
      template_type: parseInt(formData.templateType),
      tag: mapTagToNumber(formData.tag),
      layout: layout,
      params: extractedParameters.map(param => ({
        name: param.name,
        type: getParameterTypeNumber(param.type),
        sample_value: parameterValues[param.name]?.value || param.sample_value,
      })),
      note: formData.note || '',
    };

    return apiData;
  };

  // Convert components to API format
  const convertComponentsToApiFormat = (components: ZNSComponent[]) => {
    return components.map(comp => {
      const componentData: any = {};
      componentData[comp.type] = comp.data[comp.type] || comp.data;
      return componentData;
    });
  };

  // Get parameter type number
  const getParameterTypeNumber = (type: string): number => {
    const typeMap: { [key: string]: number } = {
      customer: 1,
      phone: 2,
      address: 3,
      id: 4,
      personal: 5,
      status: 6,
      contact: 7,
      time: 8,
      product: 9,
      amount: 10,
      duration: 11,
      otp: 12,
      url: 13,
      money: 14,
      bank_note: 15,
    };
    return typeMap[type] || 1;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.template_name.trim()) {
        showError({ message: 'Tên template là bắt buộc' });
        return;
      }

      if (!formData.templateType) {
        showError({ message: 'Loại template là bắt buộc' });
        return;
      }

      if (formData.components.length === 0) {
        showError({ message: 'Template phải có ít nhất 1 component' });
        return;
      }

      const apiData = convertToApiFormat();
      console.log('🚀 [EditZNSTemplateForm] Submitting:', JSON.stringify(apiData, null, 2));

      await editMutation.mutateAsync({
        integrationId,
        templateId: template.id.toString(),
        data: apiData,
      });

      showSuccess({ message: 'Chỉnh sửa template thành công!' });
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('❌ [EditZNSTemplateForm] Submit error:', error);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderComponentsStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  };

  // Step 1: Basic Info
  const renderBasicInfoStep = () => (
    <div className="space-y-4 lg:space-y-6 p-4">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Tên mẫu ZNS <span className="text-red-500">*</span>
          </label>
          <Input
            value={formData.template_name}
            onChange={e => updateFormData(prev => ({ ...prev, template_name: e.target.value }))}
            maxLength={60}
            placeholder="VD: Thông báo xác nhận đơn hàng"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Chọn Official Account <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.selectedOAId}
            onChange={value => updateFormData(prev => ({ ...prev, selectedOAId: value as string }))}
            options={[
              { value: integrationId, label: 'RedAI' }, // Default OA
            ]}
            placeholder="Chọn Official Account"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Chọn loại nội dung ZNS <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.tag}
            onChange={value => updateFormData(prev => ({ ...prev, tag: value as string }))}
            options={[
              { value: 'TRANSACTION', label: 'Giao dịch (Cấp độ 1)' },
              { value: 'CUSTOMER_CARE', label: 'Chăm sóc khách hàng (Cấp độ 2)' },
              { value: 'PROMOTION', label: 'Khuyến mãi (Cấp độ 3)' },
              { value: 'OTP', label: 'OTP (Cấp độ 1)' },
            ]}
            placeholder="Chọn loại nội dung ZNS"
          />
        </div>
      </div>
    </div>
  );

  // Step 2: Components
  const renderComponentsStep = () => {
    return (
      <div className="space-y-4 lg:space-y-6 p-4">
        <div className="mb-6">
          <Typography variant="h6" className="mb-4">
            Chọn loại template
          </Typography>
          <div className="mb-2 text-xs text-gray-500">
            Debug: Current templateType = "{formData.templateType}" | Components:{' '}
            {formData.components.length}
          </div>
          <ResponsiveGrid maxColumns={{ xs: 2, sm: 2, md: 3 }} gap={4}>
            {templateTypes.map(type => {
              const isSelected =
                formData.templateType === type.id || pendingTemplateType === type.id;

              return (
                <Card
                  key={type.id}
                  className={`p-4 cursor-pointer border-2 transition-all hover:shadow-md ${
                    isSelected
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => handleTemplateTypeChange(type.id)}
                >
                  <div className="flex flex-col items-center text-center space-y-3">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        formData.templateType === type.id
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <Icon name={type.icon as any} size="sm" />
                    </div>
                    <Typography variant="body2" className="font-medium">
                      {type.name}
                    </Typography>
                  </div>
                </Card>
              );
            })}
          </ResponsiveGrid>
        </div>

        {/* Component Selector */}
        {formData.templateType && (
          <div className="flex flex-col xl:flex-row gap-6">
            <div className="flex-1">
              <Card className="p-4">
                <Typography variant="h6" className="mb-4">
                  Thiết kế nội dung
                </Typography>
                <div className="h-[500px] overflow-y-auto border rounded-lg p-2">
                  <ZNSComponentSelector
                    // key={`component-selector-${formData.templateType}`}
                    onComponentsChange={handleComponentsChange}
                    initialComponents={formData.components}
                    templateType={formData.templateType}
                    integrationId={formData.selectedOAId || integrationId}
                    onValidationChange={handleValidationChange}
                  />
                </div>
              </Card>
            </div>

            <div className="hidden xl:block w-80 flex-shrink-0">
              <div className="sticky top-4">
                <ZNSTemplatePreviewSidebar
                  key={`preview-${formData.templateType}`}
                  templateType={formData.templateType}
                  components={formData.components}
                  isOpen={true}
                  isInline={true}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Step 3: Review
  const renderReviewStep = () => (
    <div className="space-y-6 p-4">
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Xem lại thông tin
        </Typography>
        <div className="space-y-3">
          <div>
            <Typography variant="body2" className="font-medium">
              Tên mẫu ZNS:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.template_name}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Official Account:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              RedAI
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Loại nội dung ZNS:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.tag === 'TRANSACTION' && 'Giao dịch (Cấp độ 1)'}
              {formData.tag === 'CUSTOMER_CARE' && 'Chăm sóc khách hàng (Cấp độ 2)'}
              {formData.tag === 'PROMOTION' && 'Khuyến mãi (Cấp độ 3)'}
              {formData.tag === 'OTP' && 'OTP (Cấp độ 1)'}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Loại template:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {templateTypes.find(t => t.id === formData.templateType)?.name}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="font-medium">
              Số components:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {formData.components.length}
            </Typography>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <ZNSAccordionProvider>
      <div className="h-full flex flex-col bg-background">
        {/* Header */}
        <div className="flex-shrink-0 px-4 py-3 border-b border-border bg-card">
          <div className="flex items-center justify-between">
            <Typography variant="h5" className="text-foreground">
              Chỉnh sửa ZNS Template
            </Typography>
            <IconCard
              icon="x"
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            />
          </div>
        </div>

        {/* Stepper */}
        <div className="flex-shrink-0 px-4 py-4 bg-muted/30">
          <Stepper
            steps={steps}
            currentStep={currentStep}
            onStepClick={stepId =>
              setCurrentStep(typeof stepId === 'number' ? stepId : parseInt(stepId.toString()))
            }
            size="sm"
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full p-4">{renderStepContent()}</div>
        </div>

        {/* Footer Actions */}
        <div className="flex-shrink-0 px-4 py-3 border-t border-border bg-card">
          <div className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <IconCard
                  icon="chevron-left"
                  variant="secondary"
                  size="md"
                  title="Quay lại"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                />
              )}
            </div>

            <div className="flex gap-3">
              <IconCard icon="x" variant="secondary" size="md" title="Hủy" onClick={onClose} />

              {currentStep < steps.length ? (
                <IconCard
                  icon="chevron-right"
                  variant="primary"
                  size="md"
                  title="Tiếp theo"
                  onClick={() => setCurrentStep(prev => prev + 1)}
                />
              ) : (
                <IconCard
                  icon="save"
                  variant="primary"
                  size="md"
                  title="Lưu thay đổi"
                  isLoading={editMutation.isPending}
                  onClick={handleSubmit}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Template Change Confirmation Modal */}
      <Modal
        isOpen={showTemplateChangeModal}
        onClose={cancelTemplateTypeChange}
        title="Xác nhận thay đổi template"
        size="sm"
        footer={null}
      >
        <div className="space-y-4">
          <Typography variant="body1" className="text-muted-foreground">
            Bạn có chắc chắn muốn thay đổi loại template không? Tất cả các component hiện tại sẽ bị
            xóa.
          </Typography>

          <div className="flex justify-end gap-3">
            <Button variant="secondary" size="sm" onClick={cancelTemplateTypeChange}>
              Hủy
            </Button>
            <Button variant="primary" size="sm" onClick={confirmTemplateTypeChange}>
              Xác nhận
            </Button>
          </div>
        </div>
      </Modal>
    </ZNSAccordionProvider>
  );
};

export default EditZNSTemplateForm;
