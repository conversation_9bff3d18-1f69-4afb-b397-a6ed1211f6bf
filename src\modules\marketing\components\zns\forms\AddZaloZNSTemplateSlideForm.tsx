/* eslint-disable no-case-declarations */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Input,
  Select,
  Card,
  Icon,
  ResponsiveGrid,
  Stepper,
  IconCard,
  Textarea,
  Modal,
  Button,
} from '@/shared/components/common';
import { ZNSAccordionProvider } from '@/modules/marketing/contexts/ZNSAccordionContext';
import ZNSComponentSelector from '../components/ZNSComponentSelector';
import ZNSTemplatePreviewSidebar from '../components/ZNSTemplatePreviewSidebar';
import { ZNSTemplateService } from '../../../services/zns-template.service';
import { ZaloService } from '../../../services/zalo.service';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useMutation } from '@tanstack/react-query';

interface AddZaloZNSTemplateSlideFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface TemplateFormData {
  template_name: string;
  tag: string;
  templateType: string;
  components: ZNSComponent[];
  note?: string;
  selectedOAId?: string;
}

interface ZaloOfficialAccount {
  id: string;
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  status: string;
}

interface ZNSComponent {
  id: string;
  type:
    | 'IMAGES'
    | 'LOGO'
    | 'TITLE'
    | 'PARAGRAPH'
    | 'OTP'
    | 'TABLE'
    | 'VOUCHER'
    | 'PAYMENT'
    | 'RATING'
    | 'BUTTONS';
  data: any;
}

interface TemplateType {
  id: string;
  name: string;
  icon: string;
}

interface ParameterData {
  name: string;
  type: string;
  sample_value: string;
  display_name: string;
  max_length: number;
}

const templateTypes: TemplateType[] = [
  {
    id: '1',
    name: 'marketing:zalo.zns.template.types.1',
    icon: 'settings',
  },
  {
    id: '2',
    name: 'marketing:zalo.zns.template.types.2',
    icon: 'shield',
  },
  {
    id: '3',
    name: 'marketing:zalo.zns.template.types.3',
    icon: 'credit-card',
  },
  {
    id: '4',
    name: 'marketing:zalo.zns.template.types.4',
    icon: 'credit-card',
  },
  {
    id: '5',
    name: 'marketing:zalo.zns.template.types.5',
    icon: 'gift',
  },
];

const AddZaloZNSTemplateSlideForm: React.FC<AddZaloZNSTemplateSlideFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success: showSuccess, error: showError } = useSmartNotification();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [templateTags, setTemplateTags] = useState<string[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [extractedParameters, setExtractedParameters] = useState<ParameterData[]>([]);
  const [parameterValues, setParameterValues] = useState<{
    [key: string]: { type: string; value: string };
  }>({});
  const [officialAccounts, setOfficialAccounts] = useState<ZaloOfficialAccount[]>([]);
  const [isLoadingOAs, setIsLoadingOAs] = useState(false);
  const [showValidation, setShowValidation] = useState(false);
  const [componentValidation, setComponentValidation] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });

  // Modal confirmation state
  const [showTemplateChangeModal, setShowTemplateChangeModal] = useState(false);
  const [pendingTemplateType, setPendingTemplateType] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState<TemplateFormData>({
    template_name: '',
    tag: '',
    templateType: '',
    components: [],
    note: '',
    selectedOAId: '',
  });

  // Mutations
  const createTemplateMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!formData.selectedOAId) {
        throw new Error(t('zns.validation.officialAccountRequired'));
      }
      console.log(
        '🔄 [CreateTemplateMutation] Calling API with integrationId:',
        formData.selectedOAId
      );
      console.log('🔄 [CreateTemplateMutation] Request data:', JSON.stringify(data, null, 2));

      const response = await ZNSTemplateService.createTemplate(formData.selectedOAId, data);
      console.log('✅ [CreateTemplateMutation] API Response:', response);
      return response;
    },
    onSuccess: response => {
      console.log('🎉 [CreateTemplateMutation] Success:', response);
      showSuccess({ message: 'Tạo ZNS template thành công!' });
      onSuccess?.();
    },
    onError: (error: any) => {
      console.error('❌ [CreateTemplateMutation] Error:', error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Tạo ZNS template thất bại. Vui lòng thử lại.';
      showError({ message: errorMessage });
    },
  });

  // Get tag label helper function
  const getTagLabel = useCallback((tag: string): string => {
    const tagLabels: { [key: string]: string } = {
      TRANSACTION: 'Giao dịch (Cấp độ 1)',
      CUSTOMER_CARE: 'Chăm sóc khách hàng (Cấp độ 2)',
      PROMOTION: 'Khuyến mãi (Cấp độ 3)',
      OTP: 'OTP (Cấp độ 1)',
    };
    return tagLabels[tag] || tag;
  }, []);

  // Steps configuration
  const steps = [
    {
      id: 'basic-info',
      title: t('marketing:zalo.zns.template.form.steps.basicInfo'),
      icon: 'file-text',
    },
    {
      id: 'components',
      title: t('marketing:zalo.zns.template.form.steps.components'),
      icon: 'layers',
    },
    { id: 'review', title: t('marketing:zalo.zns.template.form.steps.review'), icon: 'send' },
  ];

  // Memoized options to prevent re-renders
  const officialAccountOptions = useMemo(
    () =>
      officialAccounts.map(oa => ({
        value: oa.id,
        label: oa.name,
      })),
    [officialAccounts]
  );

  const templateTagOptions = useMemo(
    () =>
      templateTags.map(tag => ({
        value: tag,
        label: getTagLabel(tag),
      })),
    [templateTags]
  );

  // Fetch official accounts when component mounts
  useEffect(() => {
    const fetchOfficialAccounts = async () => {
      setIsLoadingOAs(true);
      try {
        const response = await ZaloService.getPaginatedAccounts({ page: 1, limit: 100 });
        console.log('Official accounts response:', response);

        if (response.code === 200 && response.result?.items) {
          const mappedAccounts = response.result.items.map(item => ({
            id: item.id.toString(),
            oaId: item.oaId,
            name: item.name,
            description: item.description,
            avatarUrl: item.avatarUrl,
            status: item.status,
          }));
          setOfficialAccounts(mappedAccounts);
        } else {
          console.warn('Invalid response format:', response);
        }
      } catch (error) {
        console.error('Error fetching official accounts:', error);
        showError({ message: 'Không thể tải danh sách Official Account' });
      } finally {
        setIsLoadingOAs(false);
      }
    };

    fetchOfficialAccounts();
  }, []); // Remove showError from dependency array

  // Fetch template tags when OA is selected
  useEffect(() => {
    const fetchTemplateTags = async () => {
      if (!formData.selectedOAId) {
        setTemplateTags([]);
        return;
      }

      setIsLoadingTags(true);
      try {
        const response = await ZNSTemplateService.getTemplateTags(formData.selectedOAId);
        console.log('Template tags response:', response);

        if (response.code === 200 && response.result?.data) {
          console.log('Setting template tags:', response.result.data);
          setTemplateTags(response.result.data);
        } else {
          console.warn('Invalid response format:', response);
        }
      } catch (error) {
        console.error('Error fetching template tags:', error);
        showError({ message: 'Không thể tải danh sách template tags' });
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchTemplateTags();
  }, [formData.selectedOAId]); // Remove showError from dependency array

  // Extract parameters from components
  const extractParametersFromComponents = useCallback(
    (components: ZNSComponent[]): ParameterData[] => {
      const parameters: ParameterData[] = [];
      const paramRegex = /<([^>]+)>/g;
      const foundParams = new Set<string>();

      components.forEach(component => {
        let textContent = '';

        // Extract text content based on component type
        switch (component.type) {
          case 'TITLE':
            textContent = component.data?.title || component.data?.content || '';
            break;
          case 'PARAGRAPH':
            textContent = component.data?.content || '';
            break;
          case 'TABLE':
            if (component.data?.rows) {
              component.data.rows.forEach((row: any) => {
                textContent += ' ' + (row.title || '') + ' ' + (row.value || '');
              });
            }
            break;
          case 'BUTTONS':
            if (component.data?.items) {
              component.data.items.forEach((item: any) => {
                textContent += ' ' + (item.title || '') + ' ' + (item.content || '');
              });
            }
            break;
          case 'OTP':
            textContent = component.data?.content || component.data?.value || '';
            if (!foundParams.has('otp')) {
              foundParams.add('otp');
              parameters.push({
                name: 'otp',
                type: 'otp',
                sample_value: '123456',
                display_name: 'Mã xác thực OTP',
                max_length: 10,
              });
            }
            break;
          case 'VOUCHER':
            textContent +=
              ' ' + (component.data?.name || '') + ' ' + (component.data?.voucher_code || '');
            break;
          case 'PAYMENT':
            textContent +=
              ' ' + (component.data?.amount || '') + ' ' + (component.data?.bank_code || '');
            break;
          case 'RATING':
            if (component.data?.items) {
              component.data.items.forEach((item: any) => {
                textContent += ' ' + (item.title || '');
              });
            }
            break;
          default:
            if (component.data) {
              const commonProps = ['content', 'text', 'title', 'value', 'description'];
              commonProps.forEach(prop => {
                if (component.data[prop]) {
                  textContent += ' ' + component.data[prop];
                }
              });
            }
            break;
        }

        // Find parameters in text content
        let match;
        paramRegex.lastIndex = 0;
        while ((match = paramRegex.exec(textContent)) !== null) {
          const paramName = match[1];
          if (!foundParams.has(paramName)) {
            foundParams.add(paramName);
            parameters.push({
              name: paramName,
              type: getParameterType(paramName),
              sample_value: getSampleValue(paramName),
              display_name: getDisplayName(paramName),
              max_length: getMaxLength(paramName),
            });
          }
        }
      });

      return parameters;
    },
    []
  );

  // Helper functions for parameter metadata
  const getParameterType = (paramName: string): string => {
    const lowerName = paramName.toLowerCase();

    // Tên khách hàng
    if (lowerName.includes('name') || lowerName.includes('customer') || lowerName.includes('ten'))
      return 'customer';

    // Số điện thoại
    if (lowerName.includes('phone') || lowerName.includes('sdt') || lowerName.includes('dienthoai'))
      return 'phone';

    // Địa chỉ
    if (lowerName.includes('address') || lowerName.includes('diachi') || lowerName.includes('addr'))
      return 'address';

    // Mã số
    if (lowerName.includes('code') || lowerName.includes('id') || lowerName.includes('ma'))
      return 'id';

    // Nhân tự chính
    if (lowerName.includes('personal') || lowerName.includes('nhantu')) return 'personal';

    // Trạng thái
    if (
      lowerName.includes('status') ||
      lowerName.includes('trangthai') ||
      lowerName.includes('state')
    )
      return 'status';

    // Thông tin liên hệ
    if (lowerName.includes('contact') || lowerName.includes('lienhe') || lowerName.includes('info'))
      return 'contact';

    // Giới tính / Danh xưng
    if (
      lowerName.includes('gender') ||
      lowerName.includes('title') ||
      lowerName.includes('gioitinh') ||
      lowerName.includes('danhxung')
    )
      return 'time';

    // Sản phẩm / Thương hiệu
    if (
      lowerName.includes('product') ||
      lowerName.includes('sanpham') ||
      lowerName.includes('brand') ||
      lowerName.includes('thuonghieu')
    )
      return 'product';

    // Số lượng / Số tiền
    if (
      lowerName.includes('amount') ||
      lowerName.includes('quantity') ||
      lowerName.includes('soluong') ||
      lowerName.includes('sotien')
    )
      return 'amount';

    // Thời gian
    if (
      lowerName.includes('time') ||
      lowerName.includes('date') ||
      lowerName.includes('thoigian') ||
      lowerName.includes('ngay')
    )
      return 'duration';

    // OTP
    if (lowerName === 'otp' || lowerName.includes('otp')) return 'otp';

    // URL
    if (lowerName.includes('url') || lowerName.includes('link') || lowerName.includes('website'))
      return 'url';

    // Tiền tệ VND
    if (
      lowerName.includes('money') ||
      lowerName.includes('price') ||
      lowerName.includes('gia') ||
      lowerName.includes('vnd')
    )
      return 'money';

    // Bank transfer note
    if (
      lowerName.includes('note') ||
      lowerName.includes('ghichu') ||
      lowerName.includes('bank') ||
      lowerName.includes('transfer')
    )
      return 'bank_note';

    return 'customer'; // Default
  };

  const getSampleValue = (paramName: string): string => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return t('marketing:zalo.zns.parameters.sampleValues.customer');
      case 'phone':
        return t('marketing:zalo.zns.parameters.sampleValues.phone');
      case 'address':
        return t('marketing:zalo.zns.parameters.sampleValues.address');
      case 'id':
        return t('marketing:zalo.zns.parameters.sampleValues.id');
      case 'personal':
        return t('marketing:zalo.zns.parameters.sampleValues.personal');
      case 'status':
        return t('marketing:zalo.zns.parameters.sampleValues.status');
      case 'contact':
        return t('marketing:zalo.zns.parameters.sampleValues.contact');
      case 'time':
        return t('marketing:zalo.zns.parameters.sampleValues.time');
      case 'product':
        return t('marketing:zalo.zns.parameters.sampleValues.product');
      case 'amount':
        return t('marketing:zalo.zns.parameters.sampleValues.amount');
      case 'duration':
        return t('marketing:zalo.zns.parameters.sampleValues.duration');
      case 'otp':
        return t('marketing:zalo.zns.parameters.sampleValues.otp');
      case 'url':
        return t('marketing:zalo.zns.parameters.sampleValues.url');
      case 'money':
        return t('marketing:zalo.zns.parameters.sampleValues.money');
      case 'bank_note':
        return t('marketing:zalo.zns.parameters.sampleValues.bank_note');
      default:
        return t('marketing:zalo.zns.parameters.sampleValues.default', { paramName });
    }
  };

  const getDisplayName = (paramName: string): string => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return t('marketing:zalo.zns.parameters.displayNames.customer');
      case 'phone':
        return t('marketing:zalo.zns.parameters.displayNames.phone');
      case 'address':
        return t('marketing:zalo.zns.parameters.displayNames.address');
      case 'id':
        return t('marketing:zalo.zns.parameters.displayNames.id');
      case 'personal':
        return t('marketing:zalo.zns.parameters.displayNames.personal');
      case 'status':
        return t('marketing:zalo.zns.parameters.displayNames.status');
      case 'contact':
        return t('marketing:zalo.zns.parameters.displayNames.contact');
      case 'time':
        return t('marketing:zalo.zns.parameters.displayNames.time');
      case 'product':
        return t('marketing:zalo.zns.parameters.displayNames.product');
      case 'amount':
        return t('marketing:zalo.zns.parameters.displayNames.amount');
      case 'duration':
        return t('marketing:zalo.zns.parameters.displayNames.duration');
      case 'otp':
        return t('marketing:zalo.zns.parameters.displayNames.otp');
      case 'url':
        return t('marketing:zalo.zns.parameters.displayNames.url');
      case 'money':
        return t('marketing:zalo.zns.parameters.displayNames.money');
      case 'bank_note':
        return t('marketing:zalo.zns.parameters.displayNames.bank_note');
      default:
        return paramName;
    }
  };

  const getMaxLength = (paramName: string): number => {
    const type = getParameterType(paramName);

    switch (type) {
      case 'customer':
        return 30;
      case 'phone':
        return 15;
      case 'address':
        return 200;
      case 'id':
        return 30;
      case 'personal':
        return 30;
      case 'status':
        return 30;
      case 'contact':
        return 50;
      case 'time':
        return 5;
      case 'product':
        return 200;
      case 'amount':
        return 20;
      case 'duration':
        return 20;
      case 'otp':
        return 10;
      case 'url':
        return 200;
      case 'money':
        return 12;
      case 'bank_note':
        return 90;
      default:
        return 50;
    }
  };

  // Update form data
  const updateFormData = useCallback((updater: (prev: TemplateFormData) => TemplateFormData) => {
    setFormData(updater);
  }, []);

  // Handle template type change with confirmation
  const handleTemplateTypeChange = useCallback(
    (newTemplateType: string) => {
      // If no current template type or no components, change directly
      if (!formData.templateType || formData.components.length === 0) {
        console.log(
          '🔄 [AddZaloZNSTemplateSlideForm] Direct template type change to:',
          newTemplateType
        );
        setExtractedParameters([]);
        updateFormData(prev => ({
          ...prev,
          templateType: newTemplateType,
          components: [],
        }));
        return;
      }

      // If changing to same type, do nothing
      if (formData.templateType === newTemplateType) {
        return;
      }

      // Show confirmation modal
      console.log(
        '🔄 [AddZaloZNSTemplateSlideForm] Requesting template type change from',
        formData.templateType,
        'to',
        newTemplateType
      );
      setPendingTemplateType(newTemplateType);
      setShowTemplateChangeModal(true);
    },
    [formData.templateType, formData.components.length, updateFormData]
  );

  // Confirm template type change
  const confirmTemplateTypeChange = useCallback(() => {
    if (pendingTemplateType) {
      console.log(
        '🔄 [AddZaloZNSTemplateSlideForm] Confirmed template type change to:',
        pendingTemplateType
      );
      setExtractedParameters([]);
      updateFormData(prev => ({
        ...prev,
        templateType: pendingTemplateType,
        components: [],
      }));
    }
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, [pendingTemplateType, updateFormData]);

  // Cancel template type change
  const cancelTemplateTypeChange = useCallback(() => {
    console.log('🔄 [AddZaloZNSTemplateSlideForm] Cancelled template type change');
    setShowTemplateChangeModal(false);
    setPendingTemplateType(null);
  }, []);

  // Handle components change - Memoized để tránh re-render
  const handleComponentsChange = useCallback(
    (components: ZNSComponent[]) => {
      console.log(
        '🔄 [AddZaloZNSTemplateSlideForm] handleComponentsChange called with:',
        components?.length,
        'components'
      );

      updateFormData(prev => {
        // Chỉ update khi thực sự có thay đổi
        if (JSON.stringify(prev.components) === JSON.stringify(components)) {
          console.log('🔄 [AddZaloZNSTemplateSlideForm] Components unchanged, skipping update');
          return prev;
        }
        console.log(
          '🔄 [AddZaloZNSTemplateSlideForm] Updating components from',
          prev.components?.length,
          'to',
          components?.length
        );
        return { ...prev, components };
      });

      // Extract parameters when components change
      const parameters = extractParametersFromComponents(components);
      setExtractedParameters(prev => {
        // Chỉ update khi thực sự có thay đổi
        if (JSON.stringify(prev) === JSON.stringify(parameters)) {
          return prev;
        }
        return parameters;
      });

      // Initialize parameter values
      const newParameterValues: { [key: string]: { type: string; value: string } } = {};
      parameters.forEach(param => {
        newParameterValues[param.name] = {
          type: param.type,
          value: param.sample_value,
        };
      });
      setParameterValues(newParameterValues);
    },
    [updateFormData, extractParametersFromComponents]
  );

  // Handle validation change from component selector - Memoized để tránh re-render
  const handleValidationChange = useCallback((isValid: boolean, errors: string[]) => {
    setComponentValidation(prev => {
      // Chỉ update khi thực sự có thay đổi
      if (prev.isValid === isValid && JSON.stringify(prev.errors) === JSON.stringify(errors)) {
        return prev;
      }
      return { isValid, errors };
    });

    // Reset validation display when validation changes
    if (isValid) {
      setShowValidation(false);
    }
  }, []);

  // Validation functions
  const canProceedFromStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return (
          formData.selectedOAId !== '' &&
          formData.template_name.trim() !== '' &&
          formData.tag !== ''
        );
      case 2:
        // Kiểm tra template type đã chọn và validation từ component selector
        // Phải có template type và validation phải valid
        // eslint-disable-next-line no-case-declarations
        const hasTemplateType = formData.templateType !== '';
        // eslint-disable-next-line no-case-declarations
        const hasValidComponents = componentValidation.isValid;
        // eslint-disable-next-line no-case-declarations
        const hasMinimumComponents = formData.components.length > 0;

        return hasTemplateType && hasValidComponents && hasMinimumComponents;
      case 3:
        return true;
      default:
        return false;
    }
  };

  // Step navigation
  const handleNext = () => {
    // Show validation errors if form is invalid
    if (!canProceedFromStep(currentStep)) {
      setShowValidation(true);
      return;
    }

    // Proceed to next step and reset validation
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
      setShowValidation(false); // Reset validation for next step
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate random tracking ID (12 digits)
  const generateTrackingId = (): string => {
    return Math.random().toString().slice(2, 14).padStart(12, '0');
  };

  // Convert tag to number format
  const getTagNumber = (tag: string): string => {
    const tagMap: { [key: string]: string } = {
      TRANSACTION: '1',
      CUSTOMER_CARE: '2',
      PROMOTION: '3',
      OTP: '1', // OTP cũng là cấp độ 1
    };
    return tagMap[tag] || '1';
  };

  // Convert components to API format
  const convertComponentsToApiFormat = (components: ZNSComponent[]) => {
    return components.map(comp => {
      const componentData: any = {};

      switch (comp.type) {
        case 'TITLE':
          componentData[comp.type] = {
            value: comp.data?.title || comp.data?.content || '',
          };
          break;

        case 'PARAGRAPH':
          componentData[comp.type] = {
            value: comp.data?.content || '',
          };
          break;

        case 'IMAGES':
          // Handle both direct items array and nested IMAGES structure
          console.log('🖼️ [Form] Processing IMAGES component data:', comp.data);
          // eslint-disable-next-line no-case-declarations
          const imageItems = comp.data?.IMAGES?.items || comp.data?.items || [];
          console.log('🖼️ [Form] Extracted image items:', imageItems);

          const processedItems = imageItems
            .map((item: any) => ({
              type: 'IMAGE',
              media_id: item.media_id || item.mediaId || item.id || '',
            }))
            .filter((item: any) => item.media_id); // Only include items with valid media_id

          console.log('🖼️ [Form] Processed items:', processedItems);

          componentData[comp.type] = {
            items: processedItems,
          };
          break;

        case 'BUTTONS':
          componentData[comp.type] = {
            items: (comp.data?.items || comp.data?.buttons || []).map((button: any) => ({
              type: button.type || 1,
              title: button.title || '',
              content: button.content || button.url || '',
            })),
          };
          break;

        case 'TABLE':
          console.log('🔧 [Form] Processing TABLE component data:', comp.data);
          componentData[comp.type] = {
            rows: comp.data?.rows || [],
          };
          console.log('🔧 [Form] TABLE converted to:', componentData[comp.type]);
          break;

        case 'OTP':
          componentData[comp.type] = {
            value: comp.data?.content || comp.data?.value || '',
          };
          break;

        case 'VOUCHER':
          console.log('🔧 [Form] Processing VOUCHER component data:', comp.data);
          componentData[comp.type] = comp.data?.VOUCHER || {
            name: '',
            condition: '',
            start_date: '',
            end_date: '',
            voucher_code: 'ABC123',
            display_code: '2',
          };
          console.log('🔧 [Form] VOUCHER converted to:', componentData[comp.type]);
          break;

        case 'PAYMENT':
          console.log('🔧 [Form] Processing PAYMENT component data:', comp.data);
          componentData[comp.type] = comp.data?.PAYMENT || {
            bank_code: '',
            account_name: '',
            bank_account: '',
            amount: '',
            note: '',
          };
          console.log('🔧 [Form] PAYMENT converted to:', componentData[comp.type]);
          break;

        case 'RATING':
          console.log('🔧 [Form] Processing RATING component data:', comp.data);
          componentData[comp.type] = comp.data?.RATING || {
            items: [],
          };
          console.log('🔧 [Form] RATING converted to:', componentData[comp.type]);
          break;

        default:
          // For other component types, try to extract common properties
          componentData[comp.type] = {
            value: comp.data?.content || comp.data?.text || comp.data?.title || '',
          };
          break;
      }

      return componentData;
    });
  };

  // Convert form data to API format
  const convertToApiFormat = (): any => {
    // Group components by section (header, body, footer)
    const headerComponents = formData.components.filter(comp => ['IMAGES'].includes(comp.type));
    const bodyComponents = formData.components.filter(comp =>
      ['TITLE', 'PARAGRAPH', 'TABLE', 'OTP', 'VOUCHER', 'PAYMENT', 'RATING'].includes(comp.type)
    );
    const footerComponents = formData.components.filter(comp => ['BUTTONS'].includes(comp.type));

    const layout: any = {};

    // Add header if has header components
    if (headerComponents.length > 0) {
      layout.header = {
        components: convertComponentsToApiFormat(headerComponents),
      };
    }

    // Always add body
    layout.body = {
      components: convertComponentsToApiFormat(bodyComponents),
    };

    // Add footer if has footer components
    if (footerComponents.length > 0) {
      layout.footer = {
        components: convertComponentsToApiFormat(footerComponents),
      };
    }

    const apiData = {
      template_name: formData.template_name,
      template_type: parseInt(formData.templateType),
      tag: getTagNumber(formData.tag),
      layout: layout,
      params: extractedParameters.map(param => ({
        name: param.name,
        type: getParameterTypeNumber(parameterValues[param.name]?.type || param.type),
        sample_value: parameterValues[param.name]?.value || param.sample_value,
      })),
      tracking_id: generateTrackingId(),
      note: formData.note || '',
    };

    return apiData;
  };

  const getParameterTypeNumber = (type: string): number => {
    const typeMap: { [key: string]: number } = {
      customer: 1, // Tên khách hàng (30)
      phone: 2, // Số điện thoại (15)
      address: 3, // Địa chỉ (200)
      id: 4, // Mã số (30)
      personal: 5, // Nhân tự chính (30)
      status: 6, // Trạng thái giao dịch (30)
      contact: 7, // Thông tin liên hệ (50)
      time: 8, // Giới tình / Danh xưng (5)
      product: 9, // Tên sản phẩm / Thương hiệu (200)
      amount: 10, // Số lượng / Số tiền (20)
      duration: 11, // Thời gian (20)
      otp: 12, // OTP (10)
      url: 13, // URL (200)
      money: 14, // Tiền tệ (VND) (12)
      bank_note: 15, // Bank transfer note (90)
    };
    return typeMap[type] || 1;
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate required fields
    if (!formData.selectedOAId) {
      showError({ message: t('zns.validation.officialAccountRequired') });
      return;
    }

    if (!formData.template_name.trim()) {
      showError({ message: t('zns.validation.templateNameRequired') });
      return;
    }

    if (!formData.tag) {
      showError({ message: t('zns.validation.contentTypeRequired') });
      return;
    }

    if (!formData.templateType) {
      showError({ message: t('zns.validation.templateTypeRequired') });
      return;
    }

    if (formData.components.length === 0) {
      showError({ message: t('zns.validation.componentsRequired') });
      return;
    }

    const apiData = convertToApiFormat();
    console.log(
      '🚀 [AddZaloZNSTemplateSlideForm] Submitting template:',
      JSON.stringify(apiData, null, 2)
    );

    createTemplateMutation.mutate(apiData);
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderComponentsStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  };

  // Step 1: Basic Info
  const renderBasicInfoStep = () => (
    <div className="space-y-4 lg:space-y-6">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.basicInfo.templateName.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Input
            placeholder={t('marketing:zalo.zns.template.form.basicInfo.templateName.placeholder')}
            value={formData.template_name}
            onChange={e => updateFormData(prev => ({ ...prev, template_name: e.target.value }))}
            className="w-full"
            error={
              showValidation && formData.template_name.trim() === ''
                ? t('marketing:zalo.zns.template.form.basicInfo.templateName.required')
                : undefined
            }
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.basicInfo.officialAccount.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.selectedOAId}
            onChange={value =>
              updateFormData(prev => ({
                ...prev,
                selectedOAId: value as string,
                tag: '', // Reset tag when OA changes
              }))
            }
            options={officialAccountOptions}
            placeholder={t(
              'marketing:zalo.zns.template.form.basicInfo.officialAccount.placeholder'
            )}
            fullWidth
            loading={isLoadingOAs}
            error={
              showValidation && formData.selectedOAId === ''
                ? t('marketing:zalo.zns.template.form.basicInfo.officialAccount.required')
                : undefined
            }
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">
            {t('marketing:zalo.zns.template.form.basicInfo.contentType.label')}{' '}
            <span className="text-red-500">*</span>
          </label>
          <Select
            value={formData.tag}
            onChange={value => updateFormData(prev => ({ ...prev, tag: value as string }))}
            options={templateTagOptions}
            placeholder={t('marketing:zalo.zns.template.form.basicInfo.contentType.placeholder')}
            fullWidth
            loading={isLoadingTags}
            disabled={!formData.selectedOAId}
            error={
              showValidation && formData.tag === ''
                ? t('marketing:zalo.zns.template.form.basicInfo.contentType.required')
                : undefined
            }
          />
        </div>
      </div>
    </div>
  );

  // Step 2: Components
  const renderComponentsStep = () => (
    <div className="space-y-4 lg:space-y-6">
      <ResponsiveGrid maxColumns={{ xs: 2, sm: 2, md: 3 }} gap={4}>
        {templateTypes.map(type => (
          <Card
            key={type.id}
            className={`p-1 cursor-pointer border-2 transition-all hover:shadow-md ${
              formData.templateType === type.id
                ? 'border-primary bg-primary/5'
                : 'border-border hover:border-primary/50'
            }`}
            onClick={() => handleTemplateTypeChange(type.id)}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div
                className={`w-6 h-6 rounded-lg flex items-center justify-center ${
                  formData.templateType === type.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <Icon name={type.icon as any} size="xs" />
              </div>
              <div>
                <Typography variant="body2" className="font-medium mb-1">
                  {t(type.name)}
                </Typography>
              </div>
            </div>
          </Card>
        ))}
      </ResponsiveGrid>

      {/* Content Design with Preview */}
      {formData.templateType && formData.selectedOAId && (
        <div className="flex flex-col xl:flex-row gap-4 xl:gap-6 h-auto xl:h-[calc(100vh-20vh)]">
          {/* Mobile: Preview First */}
          <div className="block xl:hidden">
            <div className="max-w-sm mx-auto mb-6">
              <ZNSTemplatePreviewSidebar
                key={`mobile-preview-${formData.templateType}`}
                templateType={formData.templateType}
                components={formData.components}
                isOpen={true}
                isInline={true}
              />
            </div>
          </div>

          {/* Component Selector - Flexible Width */}
          <div className="flex-1 xl:pr-4">
            <div className="space-y-3 pb-4">
              <Card className="p-3 lg:p-4 bg-card dark:bg-gray-800 border-border dark:border-gray-700">
                <Typography
                  variant="h5"
                  className="mb-3 text-lg lg:text-xl text-foreground dark:text-gray-100"
                >
                  {t('marketing:zalo.zns.template.form.components.title')}
                </Typography>
                <div
                  className="h-[400px] sm:h-[500px] xl:h-[580px] overflow-y-auto overflow-x-hidden scrollbar-hide"
                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                  <style
                    dangerouslySetInnerHTML={{
                      __html: `
                      .scrollbar-hide::-webkit-scrollbar {
                        display: none;
                      }
                    `,
                    }}
                  />
                  <ZNSComponentSelector
                    key={`component-selector-${formData.templateType}`}
                    onComponentsChange={handleComponentsChange}
                    initialComponents={formData.components}
                    templateType={formData.templateType}
                    integrationId={formData.selectedOAId}
                    onValidationChange={handleValidationChange}
                  />
                </div>

                {/* Validation Errors */}
                {showValidation && !componentValidation.isValid && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <Typography variant="body2" className="text-red-600 font-medium mb-2">
                      {t('marketing:zalo.zns.template.form.validation.incompleteTemplate')}
                    </Typography>
                    <ul className="text-sm text-red-500 space-y-1">
                      {componentValidation.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </Card>
            </div>
          </div>

          {/* Desktop: Preview Sidebar */}
          <div className="hidden xl:block w-82 mr-2 flex-shrink-0 sticky top-4 h-fit">
            <ZNSTemplatePreviewSidebar
              key={`desktop-preview-${formData.templateType}`}
              templateType={formData.templateType}
              components={formData.components}
              isOpen={true}
              isInline={true}
            />
          </div>
        </div>
      )}

      {/* Validation Errors */}
      {showValidation && !componentValidation.isValid && componentValidation.errors.length > 0 && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Icon name="alert-circle" className="text-destructive flex-shrink-0 mt-0.5" size="sm" />
            <div className="flex-1">
              <Typography variant="body2" className="text-destructive font-medium mb-2">
                {t('marketing:zalo.zns.template.form.validation.incompleteTemplate')}
              </Typography>
              <div className="space-y-1">
                {componentValidation.errors.map((error, index) => (
                  <Typography key={index} variant="caption" className="text-destructive/80 block">
                    • {error}
                  </Typography>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Step 3: Review
  const renderReviewStep = () => (
    <div className="space-y-4 lg:space-y-6 ">
      {/* Template Summary */}
      <Card className="p-3 lg:p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 border border-blue-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="flex items-center justify-center w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded">
            <svg
              className="w-3 h-3 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <Typography
            variant="h5"
            className="text-sm lg:text-base font-medium text-gray-900 dark:text-gray-100"
          >
            {t('marketing:zalo.zns.template.form.review.title')}
          </Typography>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 lg:gap-3">
          <div className="bg-white dark:bg-gray-800/50 p-2 lg:p-3 rounded border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-1 mb-1">
              <svg
                className="w-3 h-3 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"
                />
              </svg>
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {t('marketing:zalo.zns.template.form.review.templateInfo.name')}
              </span>
            </div>
            <div
              className="font-medium text-gray-900 dark:text-gray-100 text-xs lg:text-sm truncate"
              title={formData.template_name}
            >
              {formData.template_name}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800/50 p-2 lg:p-3 rounded border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-1 mb-1">
              <svg
                className="w-3 h-3 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Nội dung</span>
            </div>
            <div className="font-medium text-gray-900 dark:text-gray-100 text-xs lg:text-sm">
              {getTagLabel(formData.tag)}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800/50 p-2 lg:p-3 rounded border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-1 mb-1">
              <svg
                className="w-3 h-3 text-purple-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {t('marketing:zalo.zns.template.form.review.templateInfo.type')}
              </span>
            </div>
            <div className="font-medium text-gray-900 dark:text-gray-100 text-xs lg:text-sm">
              {t(`marketing:zalo.zns.template.types.${formData.templateType}`)}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800/50 p-2 lg:p-3 rounded border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-1 mb-1">
              <svg
                className="w-3 h-3 text-orange-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                />
              </svg>
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {t('marketing:zalo.zns.template.form.review.templateInfo.components')}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div className="font-medium text-gray-900 dark:text-gray-100 text-xs lg:text-sm">
                {formData.components.length}
              </div>
              <span className="text-xs px-1 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded">
                items
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Parameters Configuration */}
      {extractedParameters.length > 0 && (
        <Card className="p-3 lg:p-4 bg-card dark:bg-gray-800 border-border dark:border-gray-700">
          <Typography
            variant="h5"
            className="mb-3 lg:mb-4 text-base lg:text-lg font-medium text-foreground dark:text-gray-100"
          >
            {t('marketing:zalo.zns.template.form.review.parameters.title')} (
            {extractedParameters.length})
          </Typography>
          <div className="space-y-3 lg:space-y-4">
            {extractedParameters.map(param => (
              <div
                key={param.name}
                className="p-3 lg:p-4 bg-muted/30 dark:bg-gray-700/30 rounded-lg"
              >
                {/* Mobile Layout - Vertical */}
                <div className="block lg:hidden space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      {t('marketing:zalo.zns.template.form.review.parameters.name')}
                    </label>
                    <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded text-sm font-mono text-center">
                      {param.name}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      {t('marketing:zalo.zns.template.form.review.parameters.type')}
                    </label>
                    <Select
                      value={parameterValues[param.name]?.type || param.type}
                      onChange={value =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            type: value as string,
                          },
                        }))
                      }
                      options={[
                        {
                          value: 'customer',
                          label: t('marketing:zalo.zns.parameters.types.customer'),
                        },
                        { value: 'phone', label: t('marketing:zalo.zns.parameters.types.phone') },
                        {
                          value: 'address',
                          label: t('marketing:zalo.zns.parameters.types.address'),
                        },
                        { value: 'id', label: t('marketing:zalo.zns.parameters.types.id') },
                        {
                          value: 'personal',
                          label: t('marketing:zalo.zns.parameters.types.personal'),
                        },
                        { value: 'status', label: t('marketing:zalo.zns.parameters.types.status') },
                        {
                          value: 'contact',
                          label: t('marketing:zalo.zns.parameters.types.contact'),
                        },
                        { value: 'time', label: t('marketing:zalo.zns.parameters.types.time') },
                        {
                          value: 'product',
                          label: t('marketing:zalo.zns.parameters.types.product'),
                        },
                        { value: 'amount', label: t('marketing:zalo.zns.parameters.types.amount') },
                        {
                          value: 'duration',
                          label: t('marketing:zalo.zns.parameters.types.duration'),
                        },
                        { value: 'otp', label: t('marketing:zalo.zns.parameters.types.otp') },
                        { value: 'url', label: t('marketing:zalo.zns.parameters.types.url') },
                        { value: 'money', label: t('marketing:zalo.zns.parameters.types.money') },
                        {
                          value: 'bank_note',
                          label: t('marketing:zalo.zns.parameters.types.bank_note'),
                        },
                      ]}
                      size="sm"
                      fullWidth
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-1">
                      {t('marketing:zalo.zns.template.form.review.parameters.sampleValue')}
                    </label>
                    <Input
                      value={parameterValues[param.name]?.value || param.sample_value}
                      onChange={e =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            value: e.target.value,
                          },
                        }))
                      }
                      placeholder={t(
                        'marketing:zalo.zns.template.form.review.parameters.sampleValue'
                      )}
                      fullWidth
                    />
                  </div>
                </div>

                {/* Desktop Layout - Horizontal */}
                <div className="hidden lg:grid lg:grid-cols-3 lg:gap-4 lg:items-end">
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      {t('marketing:zalo.zns.template.form.review.parameters.name')}
                    </label>
                    <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded text-sm font-mono text-center">
                      {param.name}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      {t('marketing:zalo.zns.template.form.review.parameters.type')}
                    </label>
                    <Select
                      value={parameterValues[param.name]?.type || param.type}
                      onChange={value =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            type: value as string,
                          },
                        }))
                      }
                      options={[
                        {
                          value: 'customer',
                          label: t('marketing:zalo.zns.parameters.types.customer'),
                        },
                        { value: 'phone', label: t('marketing:zalo.zns.parameters.types.phone') },
                        {
                          value: 'address',
                          label: t('marketing:zalo.zns.parameters.types.address'),
                        },
                        { value: 'id', label: t('marketing:zalo.zns.parameters.types.id') },
                        {
                          value: 'personal',
                          label: t('marketing:zalo.zns.parameters.types.personal'),
                        },
                        { value: 'status', label: t('marketing:zalo.zns.parameters.types.status') },
                        {
                          value: 'contact',
                          label: t('marketing:zalo.zns.parameters.types.contact'),
                        },
                        { value: 'time', label: t('marketing:zalo.zns.parameters.types.time') },
                        {
                          value: 'product',
                          label: t('marketing:zalo.zns.parameters.types.product'),
                        },
                        { value: 'amount', label: t('marketing:zalo.zns.parameters.types.amount') },
                        {
                          value: 'duration',
                          label: t('marketing:zalo.zns.parameters.types.duration'),
                        },
                        { value: 'otp', label: t('marketing:zalo.zns.parameters.types.otp') },
                        { value: 'url', label: t('marketing:zalo.zns.parameters.types.url') },
                        { value: 'money', label: t('marketing:zalo.zns.parameters.types.money') },
                        {
                          value: 'bank_note',
                          label: t('marketing:zalo.zns.parameters.types.bank_note'),
                        },
                      ]}
                      size="sm"
                      fullWidth
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-muted-foreground mb-2">
                      {t('marketing:zalo.zns.template.form.review.parameters.sampleValue')}
                    </label>
                    <Input
                      value={parameterValues[param.name]?.value || param.sample_value}
                      onChange={e =>
                        setParameterValues(prev => ({
                          ...prev,
                          [param.name]: {
                            ...prev[param.name],
                            value: e.target.value,
                          },
                        }))
                      }
                      placeholder={t(
                        'marketing:zalo.zns.template.form.review.parameters.sampleValue'
                      )}
                      fullWidth
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Note */}
      <Card className="p-3 lg:p-4 bg-card dark:bg-gray-800 border-border dark:border-gray-700">
        <label className="block text-sm lg:text-base font-medium mb-2 lg:mb-3 text-foreground dark:text-gray-100">
          {t('marketing:zalo.zns.template.form.basicInfo.note.label')}
        </label>
        <Textarea
          value={formData.note || ''}
          onChange={e => updateFormData(prev => ({ ...prev, note: e.target.value }))}
          placeholder={t('marketing:zalo.zns.template.form.basicInfo.note.placeholder')}
          rows={3}
          fullWidth
          className="text-sm lg:text-base"
        />
      </Card>
    </div>
  );

  return (
    <ZNSAccordionProvider>
      <Card className="">
        {/* Main Content */}
        <div className="flex-1 flex flex-col w-full">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <Typography variant="h5" className="mb-1">
                {t('marketing:zalo.zns.create.title', 'Tạo ZNS Template mới')}
              </Typography>
            </div>
            <IconCard icon="x" variant="ghost" size="md" title="Đóng" onClick={onCancel} />
          </div>
          <div className=" px-6 mt-2">
            <Stepper
              steps={steps}
              currentStep={currentStep - 1}
              variant="filled"
              size="lg"
              showStepIcons
              colorScheme="primary"
              className=""
            />
          </div>
          {/* Stepper */}

          {/* Step Content */}
          <div className="flex-1 overflow-y-auto p-3 lg:p-6">{renderStepContent()}</div>

          {/* Footer */}
          <div className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <IconCard
                  icon="arrow-left"
                  variant="default"
                  size="md"
                  title={t('marketing:zalo.zns.template.form.actions.back')}
                  onClick={handlePrevious}
                />
              )}
            </div>

            <div className="flex gap-3">
              <IconCard
                icon="x"
                variant="secondary"
                size="md"
                title={t('marketing:zalo.zns.template.form.actions.cancel')}
                onClick={onCancel}
              />

              {currentStep < 3 ? (
                <IconCard
                  icon="check"
                  variant="primary"
                  size="md"
                  title={t('marketing:zalo.zns.template.form.actions.continue')}
                  onClick={handleNext}
                  disabled={!canProceedFromStep(currentStep)} // Disable if validation fails
                />
              ) : (
                <IconCard
                  icon="save"
                  variant="primary"
                  size="md"
                  title={
                    createTemplateMutation.isPending
                      ? t('marketing:zalo.zns.template.form.actions.creating')
                      : t('marketing:zalo.zns.template.form.actions.create')
                  }
                  onClick={handleSubmit}
                  disabled={createTemplateMutation.isPending}
                />
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Template Change Confirmation Modal */}
      <Modal
        isOpen={showTemplateChangeModal}
        onClose={cancelTemplateTypeChange}
        title={t('marketing:zalo.zns.componentSelector.templateChange.modal.title')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="primary" onClick={confirmTemplateTypeChange}>
              {t('marketing:zalo.zns.componentSelector.templateChange.modal.confirm')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon name="alert-triangle" className="text-amber-500" size="lg" />
            </div>
            <div className="flex-1">
              <Typography variant="body1" className="font-medium mb-2">
                {t('marketing:zalo.zns.componentSelector.templateChange.modal.warning')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:zalo.zns.componentSelector.templateChange.modal.description')}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </ZNSAccordionProvider>
  );
};

export default AddZaloZNSTemplateSlideForm;
