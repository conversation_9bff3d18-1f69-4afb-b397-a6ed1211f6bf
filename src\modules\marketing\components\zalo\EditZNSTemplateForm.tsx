import React, { useState, useEffect } from 'react';
import {
  Button,
  Input,
  Select,
  Textarea,
  Typography,
  Card,
  Icon,
  IconButton,
} from '@/shared/components/common';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

interface TemplateParam {
  name: string;
  type: number;
  sample_value: string;
}

interface LayoutComponent {
  TITLE?: { value: string };
  PARAGRAPH?: { value: string };
}

const TEMPLATE_TYPES = [
  { value: 1, label: 'ZNS tùy chỉnh' },
  { value: 2, label: 'ZNS xác thực' },
  { value: 3, label: 'ZNS yêu cầu thanh toán' },
  { value: 4, label: 'ZNS voucher' },
  { value: 5, label: 'ZNS đ<PERSON>h gi<PERSON> dịch vụ' },
];

const TAG_OPTIONS = [
  { value: '1', label: 'Transaction' },
  { value: '2', label: 'Customer care' },
  { value: '3', label: 'Promotion' },
];

const PARAM_TYPES = [
  { value: 1, label: 'Tên khách hàng (30 ký tự)' },
  { value: 2, label: 'Số điện thoại (15 ký tự)' },
  { value: 3, label: 'Địa chỉ (200 ký tự)' },
  { value: 4, label: 'Mã số (30 ký tự)' },
  { value: 5, label: 'Nhãn tùy chỉnh (30 ký tự)' },
  { value: 6, label: 'Trạng thái giao dịch (30 ký tự)' },
  { value: 7, label: 'Thông tin liên hệ (50 ký tự)' },
  { value: 8, label: 'Giới tính/Danh xưng (5 ký tự)' },
  { value: 9, label: 'Tên sản phẩm/Thương hiệu (200 ký tự)' },
  { value: 10, label: 'Số lượng/Số tiền (20 ký tự)' },
  { value: 11, label: 'Thời gian (20 ký tự)' },
  { value: 12, label: 'OTP (10 ký tự)' },
  { value: 13, label: 'URL (200 ký tự)' },
  { value: 14, label: 'Tiền tệ VNĐ (12 ký tự)' },
  { value: 15, label: 'Bank transfer note (90 ký tự)' },
];

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onClose,
  onSuccess,
}) => {
  const editMutation = useEditZNSTemplate();

  // Form state
  const [formData, setFormData] = useState({
    template_name: template.templateName || '',
    template_type: 1,
    tag: '1',
    tracking_id: '',
    note: '',
  });

  const [bodyComponents, setBodyComponents] = useState<LayoutComponent[]>([
    { TITLE: { value: '' } },
  ]);
  const [headerComponents, setHeaderComponents] = useState<LayoutComponent[]>([]);
  const [footerComponents, setFooterComponents] = useState<LayoutComponent[]>([]);
  const [params, setParams] = useState<TemplateParam[]>([]);

  // Parse existing template data
  useEffect(() => {
    if (template.templateContent) {
      try {
        const data = JSON.parse(template.templateContent);
        if (data.layout?.body?.components) {
          setBodyComponents(data.layout.body.components);
        }
        if (data.layout?.header?.components) {
          setHeaderComponents(data.layout.header.components);
        }
        if (data.layout?.footer?.components) {
          setFooterComponents(data.layout.footer.components);
        }
        if (data.params) {
          setParams(data.params);
        }
      } catch (error) {
        console.error('Error parsing template data:', error);
      }
    }
  }, [template]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addComponent = (section: 'body' | 'header' | 'footer', type: 'TITLE' | 'PARAGRAPH') => {
    const newComponent: LayoutComponent = {
      [type]: { value: '' },
    };

    if (section === 'body') {
      setBodyComponents(prev => [...prev, newComponent]);
    } else if (section === 'header') {
      setHeaderComponents(prev => [...prev, newComponent]);
    } else {
      setFooterComponents(prev => [...prev, newComponent]);
    }
  };

  const updateComponent = (
    section: 'body' | 'header' | 'footer',
    index: number,
    type: 'TITLE' | 'PARAGRAPH',
    value: string
  ) => {
    const updateComponents = (components: LayoutComponent[]) => {
      const updated = [...components];
      updated[index] = { [type]: { value } };
      return updated;
    };

    if (section === 'body') {
      setBodyComponents(updateComponents);
    } else if (section === 'header') {
      setHeaderComponents(updateComponents);
    } else {
      setFooterComponents(updateComponents);
    }
  };

  const removeComponent = (section: 'body' | 'header' | 'footer', index: number) => {
    if (section === 'body') {
      setBodyComponents(prev => prev.filter((_, i) => i !== index));
    } else if (section === 'header') {
      setHeaderComponents(prev => prev.filter((_, i) => i !== index));
    } else {
      setFooterComponents(prev => prev.filter((_, i) => i !== index));
    }
  };

  const addParam = () => {
    setParams(prev => [...prev, { name: '', type: 1, sample_value: '' }]);
  };

  const updateParam = (index: number, field: keyof TemplateParam, value: any) => {
    setParams(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const removeParam = (index: number) => {
    setParams(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    try {
      const layout: any = {
        body: { components: bodyComponents },
      };

      if (headerComponents.length > 0) {
        layout.header = { components: headerComponents };
      }

      if (footerComponents.length > 0) {
        layout.footer = { components: footerComponents };
      }

      const submitData = {
        ...formData,
        layout,
        params: params.length > 0 ? params : undefined,
      };

      await editMutation.mutateAsync({
        integrationId,
        templateId: template.id.toString(),
        data: submitData,
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error editing template:', error);
    }
  };

  const renderComponentSection = (
    title: string,
    components: LayoutComponent[],
    section: 'body' | 'header' | 'footer'
  ) => (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <Typography variant="h6">{title}</Typography>
        <div className="flex gap-2">
          <Button size="sm" variant="outline" onClick={() => addComponent(section, 'TITLE')}>
            + Title
          </Button>
          <Button size="sm" variant="outline" onClick={() => addComponent(section, 'PARAGRAPH')}>
            + Paragraph
          </Button>
        </div>
      </div>

      {components.map((component, index) => {
        const isTitle = component.TITLE;
        const type = isTitle ? 'TITLE' : 'PARAGRAPH';
        const value = isTitle ? component.TITLE?.value || '' : component.PARAGRAPH?.value || '';

        return (
          <div key={index} className="flex items-start gap-2 mb-3">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Typography variant="body2" className="font-medium">
                  {type}
                </Typography>
                <IconButton
                  icon="trash"
                  size="sm"
                  variant="ghost"
                  onClick={() => removeComponent(section, index)}
                />
              </div>
              <Textarea
                value={value}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  updateComponent(section, index, type, e.target.value)
                }
                placeholder={`Nhập nội dung ${type.toLowerCase()}...`}
                rows={3}
              />
            </div>
          </div>
        );
      })}
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h5">Chỉnh sửa ZNS Template</Typography>
        <IconButton icon="x" onClick={onClose} />
      </div>

      {/* Basic Info */}
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Thông tin cơ bản
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Tên template (10-60 ký tự)"
            value={formData.template_name}
            onChange={e => handleInputChange('template_name', e.target.value)}
            maxLength={60}
            required
          />

          <Select
            label="Loại template"
            value={formData.template_type}
            onChange={value => handleInputChange('template_type', value)}
            options={TEMPLATE_TYPES}
          />

          <Select
            label="Tag"
            value={formData.tag}
            onChange={value => handleInputChange('tag', value)}
            options={TAG_OPTIONS}
          />

          <Input
            label="Tracking ID"
            value={formData.tracking_id}
            onChange={e => handleInputChange('tracking_id', e.target.value)}
            placeholder="Mã tracking tùy chọn"
          />
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium mb-2">Ghi chú kiểm duyệt (1-400 ký tự)</label>
          <Textarea
            value={formData.note}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
              handleInputChange('note', e.target.value)
            }
            maxLength={400}
            rows={3}
            placeholder="Ghi chú cho quá trình kiểm duyệt..."
          />
        </div>
      </Card>

      {/* Layout Sections */}
      {renderComponentSection('Header (Tùy chọn)', headerComponents, 'header')}
      {renderComponentSection('Body (Bắt buộc)', bodyComponents, 'body')}
      {renderComponentSection('Footer (Tùy chọn)', footerComponents, 'footer')}

      {/* Parameters */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h6">Parameters</Typography>
          <Button size="sm" variant="outline" onClick={addParam}>
            <Icon name="plus" size="sm" className="mr-2" />
            Thêm Parameter
          </Button>
        </div>

        {params.map((param, index) => (
          <div
            key={index}
            className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border rounded-lg"
          >
            <Input
              label="Tên parameter"
              value={param.name}
              onChange={e => updateParam(index, 'name', e.target.value)}
              placeholder="customer_name"
            />

            <Select
              label="Loại parameter"
              value={param.type}
              onChange={value => updateParam(index, 'type', value)}
              options={PARAM_TYPES}
            />

            <Input
              label="Giá trị mẫu"
              value={param.sample_value}
              onChange={e => updateParam(index, 'sample_value', e.target.value)}
              placeholder="Nguyễn Văn A"
            />

            <div className="flex items-end">
              <IconButton icon="trash" variant="ghost" onClick={() => removeParam(index)} />
            </div>
          </div>
        ))}
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-4">
        <Button variant="outline" onClick={onClose}>
          Hủy
        </Button>
        <Button variant="primary" onClick={handleSubmit} isLoading={editMutation.isPending}>
          Lưu thay đổi
        </Button>
      </div>
    </div>
  );
};

export default EditZNSTemplateForm;
