import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Input, Select, Card, IconCard, Textarea } from '@/shared/components/common';
import { useEditZNSTemplate } from '../../hooks/useZNSTemplatesQuery';
import { ZNSTemplateDto } from '../../types/zalo.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface EditZNSTemplateFormProps {
  template: ZNSTemplateDto;
  integrationId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

const templateTypes = [
  { value: 1, label: 'ZNS tùy chỉnh' },
  { value: 2, label: 'ZNS xác thực' },
  { value: 3, label: 'ZNS yêu cầu thanh toán' },
  { value: 4, label: 'ZNS voucher' },
  { value: 5, label: 'ZNS đánh giá dịch vụ' },
];

const tagOptions = [
  { value: '1', label: 'Transaction' },
  { value: '2', label: 'Customer care' },
  { value: '3', label: 'Promotion' },
];

const EditZNSTemplateForm: React.FC<EditZNSTemplateFormProps> = ({
  template,
  integrationId,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { success: showSuccess, error: showError } = useSmartNotification();
  const editMutation = useEditZNSTemplate();

  // Form state
  const [formData, setFormData] = useState({
    template_name: template.templateName || '',
    template_type: 1,
    tag: '1',
    tracking_id: '',
    note: '',
  });

  const [templateContent, setTemplateContent] = useState('');
  const [parsedLayout, setParsedLayout] = useState<any>(null);
  const [parsedParams, setParsedParams] = useState<any[]>([]);

  // Parse existing template data
  useEffect(() => {
    if (template.templateContent) {
      try {
        const data = JSON.parse(template.templateContent);
        console.log('Parsed template data:', data);

        // Set form data from existing template
        if (data.template_name) {
          setFormData(prev => ({
            ...prev,
            template_name: data.template_name,
            template_type: data.template_type || 1,
            tag: data.tag?.toString() || '1',
            tracking_id: data.tracking_id || '',
            note: data.note || '',
          }));
        }

        // Parse layout
        if (data.layout) {
          setParsedLayout(data.layout);
          // Convert layout to readable format for editing
          const layoutStr = JSON.stringify(data.layout, null, 2);
          setTemplateContent(layoutStr);
        }

        // Parse params
        if (data.params) {
          setParsedParams(data.params);
        }
      } catch (error) {
        console.error('Error parsing template data:', error);
        showError({ message: 'Không thể parse dữ liệu template' });
      }
    }
  }, [template, showError]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    try {
      let layout = parsedLayout;

      // Try to parse template content if it was modified
      if (templateContent !== JSON.stringify(parsedLayout, null, 2)) {
        try {
          layout = JSON.parse(templateContent);
        } catch (error) {
          console.error('Error parsing layout JSON:', error);
          showError({ message: 'Format layout không hợp lệ' });
          return;
        }
      }

      const submitData = {
        template_name: formData.template_name,
        template_type: formData.template_type,
        tag: formData.tag,
        layout: layout,
        params: parsedParams,
        tracking_id: formData.tracking_id || undefined,
        note: formData.note || undefined,
      };

      console.log('Submitting edit data:', submitData);

      await editMutation.mutateAsync({
        integrationId,
        templateId: template.id.toString(),
        data: submitData,
      });

      showSuccess({ message: 'Chỉnh sửa template thành công!' });
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error editing template:', error);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <Typography variant="h5">Chỉnh sửa ZNS Template</Typography>
        <IconCard icon="x" variant="ghost" onClick={onClose} />
      </div>

      {/* Basic Info */}
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Thông tin cơ bản
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Tên template (10-60 ký tự)"
            value={formData.template_name}
            onChange={e => handleInputChange('template_name', e.target.value)}
            maxLength={60}
            required
          />

          <Select
            label="Loại template"
            value={formData.template_type}
            onChange={value => handleInputChange('template_type', value)}
            options={templateTypes}
          />

          <Select
            label="Tag"
            value={formData.tag}
            onChange={value => handleInputChange('tag', value)}
            options={tagOptions}
          />

          <Input
            label="Tracking ID"
            value={formData.tracking_id}
            onChange={e => handleInputChange('tracking_id', e.target.value)}
            placeholder="Mã tracking tùy chọn"
          />
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium mb-2">Ghi chú kiểm duyệt (1-400 ký tự)</label>
          <Textarea
            value={formData.note}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
              handleInputChange('note', e.target.value)
            }
            maxLength={400}
            rows={3}
            placeholder="Ghi chú cho quá trình kiểm duyệt..."
          />
        </div>
      </Card>

      {/* Template Layout */}
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Layout Template
        </Typography>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Layout JSON (Chỉnh sửa cẩn thận)
            </label>
            <Textarea
              value={templateContent}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setTemplateContent(e.target.value)
              }
              rows={15}
              placeholder="Layout JSON..."
              className="font-mono text-sm"
            />
          </div>

          {parsedParams.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">Parameters hiện tại</label>
              <div className="bg-gray-50 p-3 rounded-md">
                <pre className="text-sm">{JSON.stringify(parsedParams, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-4">
        <IconCard icon="x" variant="secondary" onClick={onClose} title="Hủy" />
        <IconCard
          icon="check"
          variant="primary"
          onClick={handleSubmit}
          title="Lưu thay đổi"
          isLoading={editMutation.isPending}
        />
      </div>
    </div>
  );
};

export default EditZNSTemplateForm;
