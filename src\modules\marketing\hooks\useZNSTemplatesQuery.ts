import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ZNSTemplateDto, ZNSTemplateQueryDto } from '../types/zalo.types';
import { ZALO_QUERY_KEYS, ZALO_DEFAULTS } from '../constants/zalo.constants';
import {
  getZNSTemplatesWithBusinessLogic,
  getZNSTemplateByIdWithValidation,
  createZNSTemplateWithValidation,
  updateZNSTemplateWithValidation,
  deleteZNSTemplateWithValidation,
  bulkDeleteZNSTemplatesWithValidation,
  syncZNSTemplatesWithValidation,
} from '../services/zns-templates.service';
import { editZNSTemplate } from '../api/zalo/znsTemplatesApi';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Hooks Layer - ZNS Templates
 * Sử dụng TanStack Query để quản lý state và cache
 */

/**
 * Hook lấy danh sách ZNS templates
 */
export const useZNSTemplates = (params?: ZNSTemplateQueryDto) => {
  return useQuery({
    queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_ALL((params as Record<string, unknown>) || {}),
    queryFn: () => getZNSTemplatesWithBusinessLogic(params),
    staleTime: ZALO_DEFAULTS.STALE_TIME,
    gcTime: ZALO_DEFAULTS.CACHE_TIME,
    enabled: true,
  });
};

/**
 * Hook lấy chi tiết ZNS template
 */
export const useZNSTemplate = (templateId: string) => {
  return useQuery({
    queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_DETAIL(templateId),
    queryFn: () => getZNSTemplateByIdWithValidation(templateId),
    staleTime: ZALO_DEFAULTS.STALE_TIME,
    gcTime: ZALO_DEFAULTS.CACHE_TIME,
    enabled: !!templateId,
  });
};

/**
 * Hook tạo ZNS template mới
 */
export const useCreateZNSTemplate = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: createZNSTemplateWithValidation,
    onSuccess: () => {
      // Invalidate và refetch danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      showSuccess({ message: 'Tạo ZNS template thành công' });
    },
    onError: (error: Error) => {
      showError({ message: `Tạo ZNS template thất bại: ${error.message}` });
    },
  });
};

/**
 * Hook cập nhật ZNS template
 */
export const useUpdateZNSTemplate = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: ({
      templateId,
      data,
    }: {
      templateId: string;
      data: Partial<Omit<ZNSTemplateDto, 'id' | 'createdAt' | 'updatedAt'>>;
    }) => updateZNSTemplateWithValidation(templateId, data),
    onSuccess: (_, variables) => {
      // Invalidate danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      // Invalidate chi tiết template
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_DETAIL(variables.templateId),
      });

      showSuccess({ message: 'Cập nhật ZNS template thành công' });
    },
    onError: (error: Error) => {
      showError({ message: `Cập nhật ZNS template thất bại: ${error.message}` });
    },
  });
};

/**
 * Hook xóa ZNS template
 */
export const useDeleteZNSTemplate = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: deleteZNSTemplateWithValidation,
    onSuccess: (_, templateId) => {
      // Invalidate danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      // Remove chi tiết template khỏi cache
      queryClient.removeQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_DETAIL(templateId),
      });

      showSuccess({ message: 'Xóa ZNS template thành công' });
    },
    onError: (error: Error) => {
      showError({ message: `Xóa ZNS template thất bại: ${error.message}` });
    },
  });
};

/**
 * Hook xóa nhiều ZNS templates
 */
export const useBulkDeleteZNSTemplates = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: bulkDeleteZNSTemplatesWithValidation,
    onSuccess: (_, templateIds) => {
      // Invalidate danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      // Remove chi tiết templates khỏi cache
      templateIds.forEach(templateId => {
        queryClient.removeQueries({
          queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_DETAIL(templateId.toString()),
        });
      });

      showSuccess({
        message: `Xóa ${templateIds.length} ZNS template thành công`,
      });
    },
    onError: (error: Error) => {
      showError({ message: `Xóa ZNS templates thất bại: ${error.message}` });
    },
  });
};

/**
 * Hook chỉnh sửa ZNS template đã bị từ chối (REJECT)
 */
export const useEditZNSTemplate = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: ({
      integrationId,
      templateId,
      data,
    }: {
      integrationId: string;
      templateId: string;
      data: {
        template_name: string;
        template_type: number;
        tag: string;
        layout: {
          body: {
            components: Array<{
              TITLE?: { value: string };
              PARAGRAPH?: { value: string };
            }>;
          };
          header?: {
            components: Array<{
              TITLE?: { value: string };
              PARAGRAPH?: { value: string };
            }>;
          };
          footer?: {
            components: Array<{
              TITLE?: { value: string };
              PARAGRAPH?: { value: string };
            }>;
          };
        };
        params?: Array<{
          name: string;
          type: number;
          sample_value: string;
        }>;
        tracking_id?: string;
        note?: string;
      };
    }) => {
      return editZNSTemplate(integrationId, templateId, data);
    },
    onSuccess: (_, variables) => {
      // Invalidate danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      // Invalidate chi tiết template
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATE_DETAIL(variables.templateId),
      });

      showSuccess({ message: 'Chỉnh sửa ZNS template thành công' });
    },
    onError: (error: Error) => {
      showError({ message: `Chỉnh sửa ZNS template thất bại: ${error.message}` });
    },
  });
};

/**
 * Hook đồng bộ ZNS templates từ Zalo API
 */
export const useSyncZNSTemplates = () => {
  const queryClient = useQueryClient();
  const { success: showSuccess, error: showError } = useSmartNotification();

  return useMutation({
    mutationFn: () => syncZNSTemplatesWithValidation(),
    onSuccess: response => {
      // Invalidate và refetch danh sách templates
      queryClient.invalidateQueries({
        queryKey: ZALO_QUERY_KEYS.ZNS_TEMPLATES(),
      });

      const syncedCount = response.result?.syncedCount || 0;
      showSuccess({
        message: `Đồng bộ thành công ${syncedCount} ZNS template từ Zalo API`,
      });
    },
    onError: (error: Error) => {
      showError({
        message: `Đồng bộ ZNS templates thất bại: ${error.message}`,
      });
    },
  });
};
